drop table if exists backlite_tasks;
drop table if exists backlite_tasks_completed;
drop table if exists m_users;
drop table if exists password_tokens;
drop table if exists m_pref;
drop table if exists m_locations;
drop table if exists m_group;
drop table if exists t_allocation_runs;
drop table if exists t_solutions;
drop table if exists t_solution_details;
drop table if exists m_items;
drop table if exists m_shipper;
drop table if exists m_pickups;
drop table if exists m_pickup_group;
drop table if exists m_dest_company;
drop table if exists m_dest_area;
drop table if exists m_dest_spot;
drop table if exists m_dest_group;
drop table if exists m_distances;
drop table if exists t_orders;
drop table if exists m_drivers;
drop table if exists driver_material_categories;
drop table if exists m_vehicle_types;
drop table if exists m_vehicle_categories;
drop table if exists m_vehicle_weight_categories;
drop table if exists m_vehicle_tank_materials;
drop table if exists m_vehicle_alkaline;
drop table if exists m_vehicles;
drop table if exists vehicle_material_categories;
drop table if exists driver_items;
drop table if exists driver_vehicles;
drop table if exists process_details;
drop table if exists m_salary;
drop table if exists m_patterns;


create table backlite_tasks
(
    id               text              not null primary key,
    created_at       integer           not null,
    queue            text              not null,
    task             blob              not null,
    wait_until       integer,
    claimed_at       integer,
    last_executed_at integer,
    attempts         integer default 0 not null
) strict;

create index idx_backlite_tasks_wait_until on backlite_tasks (wait_until) where wait_until IS NOT NULL;

create table backlite_tasks_completed
(
    id                  text    not null primary key,
    created_at          integer not null,
    queue               text    not null,
    last_executed_at    integer,
    attempts            integer not null,
    last_duration_micro integer,
    succeeded           integer,
    task                blob,
    expires_at          integer,
    error               text
) strict;


create table m_pref
(
    id            integer                                   not null primary key autoincrement,
    name          text,
    display_order integer,
    roms_id       integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);

create table m_locations
(
    id         integer                                   not null primary key autoincrement,
    lat_lng    text                                      not null,
    zip        text,
    pref_id    integer
        constraint fk_m_locations_pref_id references m_pref (id),
    city_cd    text,
    add1       text,
    add1_kana  text,
    add2       text,
    add2_kana  text,
    add3       text,
    add3_kana  text,
    tel        text,
    fax        text,
    unique_id  text,
    type       text,
    type_id    integer,

    created_at integer   default (strftime('%s', 'now')) not null,
    created_by integer,
    updated_at integer,
    updated_by integer,
    deleted_at timestamp default null
);

create table m_group
(
    id          integer                                   not null primary key autoincrement,
    nm          text                                      not null,
    group_nm    text,
    group_kana  text,
    group_type  integer,
    item_cat    text,
    location_id integer
        constraint fk_m_group_location_id references m_locations (id),
    opt1        text,
    opt2        text,
    roms_id     integer,


    created_at  integer   default (strftime('%s', 'now')) not null,
    created_by  integer,
    updated_at  integer,
    updated_by  integer,
    deleted_at  timestamp default null
);

create index idx_m_group_group_type on m_group (group_type);
create index idx_m_group_item_cat on m_group (item_cat);
create index idx_m_group_deleted_at on m_group (deleted_at);


create table m_users
(
    id         integer                                      not null primary key autoincrement,
    name       text                                         not null,
    email      text                                         not null,
    password   text                                         not null,
    status     varchar(255) default 'CONFIRMATION_REQUIRED' not null,
    is_admin   boolean      default false                   not null,

    created_at integer      default (strftime('%s', 'now')) not null,
    created_by integer,
    updated_at integer,
    updated_by integer,
    deleted_at timestamp    default null,
    check (status in ('REGISTERED', 'CONFIRMATION_REQUIRED', 'DELETED', 'LOCKED'))
);

create table password_tokens
(
    id                  integer  not null primary key autoincrement,
    hash                text     not null,
    created_at          datetime not null,
    password_token_user integer  not null
        constraint password_tokens_users_user references m_users
);

create unique index u_idx_m_users_email on m_users (email);


create table t_allocation_runs
(
    id                   integer not null primary key autoincrement,
    fulfill_date         date    not null,
    status               smallint,
    with_history         boolean not null default false,
    with_even_allocation boolean not null default false,

    created_at           integer          default (strftime('%s', 'now')) not null,
    created_by           integer,
    updated_at           integer,
    updated_by           integer,
    deleted_at           timestamp        default null
);

create table t_solutions
(
    id          integer                                   not null primary key autoincrement,
    run_id      integer                                   not null
        constraint fk_t_solutions_run_id references t_allocation_runs (id),
    total_time  integer,
    score       text,
    is_feasible int,
    is_manual   int,

    created_at  integer   default (strftime('%s', 'now')) not null,
    created_by  integer,
    updated_at  integer,
    updated_by  integer,
    deleted_at  timestamp default null
);


create table t_solution_details
(
    id                 integer not null primary key autoincrement,
    solution_id        integer not null
        constraint fk_t_solution_details_solution_id references t_solutions (id),

    vehicle_id         integer not null references m_vehicles,
    driver_id          integer not null references m_drivers,
    order_id           text    not null references t_orders,
    visit_index        integer not null,
    location_unique_id text    not null,
    arrival_time       integer not null,
    service_time       integer not null,
    departure_time     integer not null,
    time_to_depot      integer
);


create table m_items
(
    id           integer not null primary key autoincrement,
    name         text    not null,
    snm1         text,
    snm2         text,
    category     integer,
    rolly2_flg   boolean          default false,
    rolly1_flg   boolean          default false,
    rolly3_flg   boolean          default false,
    bcolor       text,
    fcolor       text,
    sort         integer,
    opt2         text,
    opt3         text,
    poison_flg   boolean not null default false,
    cleaning_flg boolean not null default false,
    roms_id      integer,

    created_at   integer          default (strftime('%s', 'now')) not null,
    created_by   integer,
    updated_at   integer,
    updated_by   integer,
    deleted_at   timestamp        default null
);

create index idx_m_items_category on m_items (category);
create index idx_m_items_rolly_flags on m_items (rolly1_flg, rolly2_flg, rolly3_flg);
create index idx_m_items_deleted_at on m_items (deleted_at);
-- create unique index u_idx_m_items_name ON m_items (name); --  WHERE deleted_at IS NULL;


create table m_shipper
(
    id          integer                                   not null primary key autoincrement,
    charge_cd   text                                      not null,
    nm          text                                      not null,
    kana        text,
    snm1        text,
    snm2        text,
    location_id integer                                   not null
        constraint fk_m_shipper_location_id references m_locations (id),

    opt1        text,
    opt2        text,
    opt3        text,
    opt4        text,
    opt5        text,
    roms_id     integer,

    created_at  integer   default (strftime('%s', 'now')) not null,
    created_by  integer,
    updated_at  integer,
    updated_by  integer,
    deleted_at  timestamp default null
);

create index idx_m_shipper_deleted_at on m_shipper (deleted_at);


create table m_pickups
(
    id          integer                                   not null primary key autoincrement,
    nm          text,
    kana        text,
    snm1        text,
    snm2        text,
    location_id integer                                   not null
        constraint fk_m_pickups_location_id references m_locations (id),

    start_time  text,
    lunch_time  text,
    end_time    text,
    opt1        text,
    opt2        text,
    opt3        text,
    roms_id     integer,

    created_at  integer   default (strftime('%s', 'now')) not null,
    created_by  integer,
    updated_at  integer,
    updated_by  integer,
    deleted_at  timestamp default null
);

create index idx_m_pickup_deleted_at on m_pickups (deleted_at);


create table m_pickup_group
(
    pickup_id integer not null references m_pickups on delete cascade,
    group_id  integer not null references m_group,
    primary key (pickup_id, group_id)
);


create table m_dest_company
(
    id         integer                                   not null primary key autoincrement,
    name       text                                      not null,
    kana       text,
    opt1       text,
    opt2       text,
    opt3       text,
    roms_id    integer,

    created_at integer   default (strftime('%s', 'now')) not null,
    created_by integer,
    updated_at integer,
    updated_by integer,
    deleted_at timestamp default null
);

create index idx_m_dest_company_deleted_at on m_dest_company (deleted_at);



create table m_dest_area
(
    id              integer                                   not null primary key autoincrement,
    dest_company_id integer                                   not null references m_dest_company,
    serial_no       integer   default 1,
    nm              text,
    kana            text,

    opt1            text,
    opt2            text,
    opt3            text,
    location_id     integer                                   not null
        constraint fk_m_dest_area_location_id references m_locations (id),
    roms_id         integer,

    created_at      integer   default (strftime('%s', 'now')) not null,
    created_by      integer,
    updated_at      integer,
    updated_by      integer,
    deleted_at      timestamp default null
);

create index idx_m_dest_area_deleted_at on m_dest_area (deleted_at);
create unique index u_idx_m_dest_area_company_id_serial_no on m_dest_area (dest_company_id, serial_no);



create table m_dest_spot
(
    id           integer                                   not null primary key autoincrement,
    dest_area_id integer                                   not null references m_dest_area,
    nm           text,
    spot         text,
    item_id      integer
        constraint fk_m_dest_spot_item_id references m_items (id),
    kana1        text,
    kana2        text,
    snm1         text,
    snm2         text,
    dept1        text,
    dept2        text,
    person1      text,
    person2      text,
    contact1     text,
    contact2     text,
    cmt1_flg     boolean   default false,
    cmt1         text,
    cmt2_flg     boolean   default false,
    cmt2         text,
    cmt3_flg     boolean   default false,
    cmt3         text,
    cmt4_flg     boolean   default false,
    cmt4         text,
    cmt5_flg     boolean   default false,
    cmt5         text,
    cmt6_flg     boolean   default false,
    cmt6         text,
    cmt7_flg     boolean   default false,
    cmt7         text,
    cmt8_flg     boolean   default false,
    cmt8         text,
    cmt9_flg     boolean   default false,
    cmt9         text,
    cmt10_flg    boolean   default false,
    cmt10        text,
    file_id      text,
    file1        text,
    file2        text,
    remarks      text,
    roms_id      integer,

    created_at   integer   default (strftime('%s', 'now')) not null,
    created_by   integer,
    updated_at   integer,
    updated_by   integer,
    deleted_at   timestamp default null
);


create index idx_m_dest_spot_deleted_at on m_dest_spot (deleted_at);



create table m_dest_group
(
    id           integer                                   not null primary key autoincrement,
    dest_spot_id integer                                   not null references m_dest_spot,
    group_id     integer                                   not null references m_group,
    dest_cd      text                                      not null,
    shipper_id   integer                                   not null references m_shipper,
    keisu        text,
    fare_type    text,
    fare_dist    text,
    fare_price   text,
    remarks      text,
    roms_id      integer,

    created_at   integer   default (strftime('%s', 'now')) not null,
    created_by   integer,
    updated_at   integer,
    updated_by   integer,
    deleted_at   timestamp default null
);


create index idx_m_dest_group_deleted_at on m_dest_group (deleted_at);



create table m_distances
(
    from_loc_id integer                                   not null,
    to_loc_id   integer                                   not null,
    duration    integer                                   not null,

    created_at  integer   default (strftime('%s', 'now')) not null,
    created_by  integer,
    updated_at  integer,
    updated_by  integer,
    deleted_at  timestamp default null,

    primary key (from_loc_id, to_loc_id),
    foreign key (from_loc_id) references m_locations (id),
    foreign key (to_loc_id) references m_locations (id)
);

/*create table t_orders
(
    id             integer                                   not null primary key autoincrement,
    pickup_id      integer                                   not null,
    destination_id integer                                   not null,
    item_id        integer                                   not null,
    fulfill_date   date                                      not null,

    created_at     integer   default (strftime('%s', 'now')) not null,
    created_by     integer,
    updated_at     integer,
    updated_by     integer,
    deleted_at     timestamp default null,

    foreign key (pickup_id) references m_pickups (id),
    foreign key (destination_id) references m_dest_group (id),
    foreign key (item_id) references m_items (id)
);*/

create table t_orders
(
    id           integer                                   not null primary key autoincrement,
    roms_id      text                                      not null,

    type_no      integer,
    import_key   text,
    dest_cd      text                                      not null,
    item_id      integer                                   not null,
    pickup_id    integer                                   not null
        constraint t_order_fkey3
            references m_pickups,
    volume1      text                                      not null,
    unit1        text                                      not null,
    volume2      text,
    unit2        integer,
    requirement  text,
    ship_date    date                                      not null,
    deli_date    date                                      not null,
    set_time     text,
    cmt1         text,
    cmt2         text,
    remarks      text,
    order_status smallint  default 1,
    p_no         integer,
    opt1         text,
    opt2         text,
    opt3         text,

    lock_u_id    text,


    created_at   integer   default (strftime('%s', 'now')) not null,
    created_by   integer,
    updated_at   integer,
    updated_by   integer,
    deleted_at   timestamp default null,


    foreign key (pickup_id) references m_pickups (id),
    foreign key (dest_cd) references m_dest_group (dest_cd),
    foreign key (item_id) references m_items (id)
);


create index t_order_idx1 on t_orders (dest_cd);
create index t_order_idx2 on t_orders (item_id);
create index t_order_idx3 on t_orders (pickup_id);
create index t_order_idx4 on t_orders (deli_date);
create index t_order_idx5 on t_orders (p_no);
create index t_order_idx6 on t_orders (deleted_at);
create index t_order_idx7 on t_orders (type_no);



create table m_salary
(
    id         integer                                   not null primary key autoincrement,
    class      integer                                   not null,
    rank       integer                                   not null,
    base       integer   default 0                       not null,
    g_1        boolean   default false,
    g_2        boolean   default false,
    g_3        boolean   default false,
    g_4        boolean   default false,
    g_5        boolean   default false,
    roms_id    integer,

    created_at integer   default (strftime('%s', 'now')) not null,
    created_by integer,
    updated_at integer,
    updated_by integer,
    deleted_at timestamp default null
);


create index m_salary_idx1 on m_salary (g_1);
create index m_salary_idx2 on m_salary (g_2);
create index m_salary_idx3 on m_salary (g_3);
create index m_salary_idx4 on m_salary (g_4);
create index m_salary_idx5 on m_salary (g_5);
create index m_salary_idx6 on m_salary (deleted_at);


create table m_drivers
(
    id            integer                                   not null primary key autoincrement,
    employee_cd   text,
    group_id      integer                                   not null references m_group,
    nm1           text,
    nm2           text,
    kana2         text,
    snm           text,
    job_type      integer,
    belong_flg    boolean   default true,
    salary_id     integer references m_salary,
    keisu         double precision,
    vehicle_id    integer references m_vehicles,
    location_id   integer                                   not null
        constraint fk_m_drivers_location_id references m_locations (id),

    mobile        text,
    cmt_job       text,
    remarks       text,
    employed_date date,
    quit_flg      boolean   default false,
    quit_date     date,
    opt1          text,
    opt2          text,
    opt3          text,
    roms_id       integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);


create index m_drivers_idx1 on m_drivers (employee_cd);
create index m_drivers_idx2 on m_drivers (deleted_at);


create table m_vehicle_types
(
    id            integer                                   not null primary key autoincrement,
    name          text,
    display_order integer,
    roms_id       integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);

create table m_vehicle_categories
(
    id            integer                                   not null primary key autoincrement,
    name          text,
    display_order integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);

create table m_vehicle_weight_categories
(
    id            integer                                   not null primary key autoincrement,
    name          text,
    display_order integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);

create table m_vehicle_tank_materials
(
    id            integer                                   not null primary key autoincrement,
    name          text,
    display_order integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);

create table m_vehicle_alkaline
(
    id            integer                                   not null primary key autoincrement,
    name          text,
    display_order integer,

    created_at    integer   default (strftime('%s', 'now')) not null,
    created_by    integer,
    updated_at    integer,
    updated_by    integer,
    deleted_at    timestamp default null
);


create table m_vehicles
(
    id                    integer                                   not null primary key autoincrement,
    group_id              integer                                   not null references m_group,
    serial_nm             text,
    number                text,
    number_plate          text,
    belong_flg            boolean   default true,
    vehicle_type          integer,
    tank_type             text,
    tank_limit1           text,
    tank_limit2           text,
    total_limit           text,

    name                  text,
    model                 text,
    initial_ym            text,
    inspect_ym            text,
    remarks               text,
    tank_size1            text,
    tank_size2            text,
    gas                   text,
    display_order         integer,
    vehicle_category      integer references m_vehicle_categories (id),
    weight_category       integer references m_vehicle_weight_categories (id),
    tank_material         integer references m_vehicle_tank_materials (id),
    alkaline              integer references m_vehicle_alkaline (id),
    maximum_load_capacity integer,
    front_tank_capacity   integer,
    rear_tank_capacity    integer,
    total_tank_capacity   integer,
    roms_id               integer,


    created_at            integer   default (strftime('%s', 'now')) not null,
    created_by            integer,
    updated_at            integer,
    updated_by            integer,
    deleted_at            timestamp default null
);

create table driver_material_categories
(
    driver_id integer not null,
    item_id   integer not null,
    primary key (driver_id, item_id),
    foreign key (driver_id) references m_drivers (id),
    foreign key (item_id) references m_items (id)
);

create table vehicle_material_categories
(
    vehicle_id integer not null,
    item_id    integer not null,
    primary key (vehicle_id, item_id),
    foreign key (vehicle_id) references m_vehicles (id),
    foreign key (item_id) references m_items (id)
);

create table driver_items
(
    driver_id integer not null,
    item_id   integer not null,
    primary key (driver_id, item_id),
    foreign key (driver_id) references m_drivers (id),
    foreign key (item_id) references m_items (id)
);

create table driver_vehicles
(
    driver_id  integer not null,
    vehicle_id integer not null,
    primary key (driver_id, vehicle_id),
    foreign key (driver_id) references m_drivers (id),
    foreign key (vehicle_id) references m_vehicles (id)
);

create table m_patterns
(
    item_id     integer                                   not null,
    shipper_id  integer                                   not null,
    pickup_id   integer                                   not null,
    delivery_id integer                                   not null,
    driver_id   integer                                   not null,
    vehicle_id  integer                                   not null,


    created_at  integer   default (strftime('%s', 'now')) not null,
    created_by  integer,
    updated_at  integer,
    updated_by  integer,
    deleted_at  timestamp default null,


    primary key (item_id, shipper_id, pickup_id, delivery_id, driver_id, vehicle_id),
    foreign key (item_id) references m_items (id),
    foreign key (shipper_id) references m_shipper (id),
    foreign key (pickup_id) references m_locations (id),
    foreign key (delivery_id) references m_locations (id),
    foreign key (driver_id) references m_drivers (id),
    foreign key (vehicle_id) references m_vehicles (id)
);

create table process_details
(
    id          integer not null primary key autoincrement,
    type        integer not null,
    description text,
    start       timestamp        default current_timestamp not null,
    end         timestamp,
    status      integer not null default 0
);


create view v_order
            (type_no, order_type, file_nm, type_remarks, order_id, item_cd, item_nm, item_snm, item_sort, category,
             dest_cd, pickup_cd, pickup_nm, pickup_snm, volume1, unit1, volume2, unit2, requirement, ship_date,
             deli_date, set_time, cmt1, cmt2, updater_id, remarks, regist_ymd, update_ymd, delete_flg, g_no, shipper_cd,
             shipper_snm, nm, kana1, snm1, spot, add1, add2, add3, tel, fax, dest_remarks, order_status, p_no,
             lock_u_id, partner_name, cnt, driver_name)
as

SELECT o.id,
       o.pickup_id,
       dg.id                                                as destination_id,
       dg.shipper_id 										as shipper_id,
       sh.snm1 												as shipper_name,
       o.dest_cd                                            AS destination_cd,
       o.item_id,
       o.deli_date                                          AS fulfill_date,
       o.set_time                                           AS set_time,
       o.volume1,
       o.unit1,
       o.volume2,
       o.unit2,


       pl.id                                                AS pickup_location__id,
       pl.unique_id											AS pickup_location__unique_id,
       p.nm                                                 AS pickup_location__name,
       p.snm1                                               AS pickup_location__short_name,
       substr(pl.lat_lng, 1, instr(pl.lat_lng, ', ') - 1)   as pickup_location__lat,
       substr(pl.lat_lng, instr(pl.lat_lng, ', ') + 2)      as pickup_location__lon,
       concat_ws('、', ifnull(pl.add1, ''),
                 ifnull(pl.add2, ''), ifnull(pl.add3, ''))  as pickup_location__address,


       dal.id                                               AS destination_location__id,
       dal.unique_id										AS destination_location__unique_id,
       dd.snm1                                              AS destination_location__name,
       dd.snm1												AS destination_location__short_name,
       substr(dal.lat_lng, 1, instr(dal.lat_lng, ', ') - 1) as destination_location__lat,
       substr(dal.lat_lng, instr(dal.lat_lng, ', ') + 2)    as destination_location__lon,
       concat_ws('、', ifnull(dal.add1, ''), ifnull(dal.add2, ''),
                 ifnull(dal.add3, ''))                      as destination_location__address,


       i.id                                                 AS item__id,
       i.name                                               AS item__name,
       i.cleaning_flg 										AS item__cleaning_flag
FROM t_orders AS o
         JOIN m_pickups AS p ON (p.id = o.pickup_id) AND p.deleted_at IS NULL
         JOIN m_locations AS pl ON (pl.id = p.location_id) AND pl.deleted_at IS NULL AND pl.lat_lng is not null and
                                   trim(pl.lat_lng) <> '-'
         JOIN m_dest_group AS dg ON (dg.dest_cd = o.dest_cd) AND dg.deleted_at IS NULL
         JOIN m_shipper sh on sh.id = dg.shipper_id and sh.deleted_at is null
         JOIN m_dest_spot AS dd ON (dest_spot_id = dd.id) AND dd.deleted_at IS NULL
         JOIN m_dest_area AS da ON (da.id = dd.dest_area_id) AND da.deleted_at IS NULL
         JOIN m_locations dal on dal.id = da.location_id and dal.deleted_at is null AND dal.lat_lng is not null and
                                 trim(dal.lat_lng) <> '-'
         JOIN m_items AS i ON (i.id = o.item_id) AND i.deleted_at IS NULL

WHERE (deli_date = '2025-03-05') AND o.deleted_at IS NULL AND dg.group_id = 1 ORDER BY o.id;
