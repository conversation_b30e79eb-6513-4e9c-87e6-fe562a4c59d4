@import "admin-lte/dist/css/adminlte.min.css";

/*
import "bootstrap-icons/font/bootstrap-icons.css";
import "unpoly/unpoly.min.css";
import "unpoly/unpoly-bootstrap5.min.css";
import "tom-select/dist/css/tom-select.bootstrap5.min.css";
import "sweetalert2/dist/sweetalert2.min.css";
import "vis-timeline/dist/vis-timeline-graph2d.min.css";*/

.card-transparent {
  background-color: transparent;
  border: none; /* Optionally remove border */
}

.card-transparent .card-body {
  background-color: transparent;
}

.bi {
  font-size: 18px;
}

#map {
  height: 70vh;
}

.fade-me-out.htmx-swapping {
  opacity: 0;
  transition: opacity 0.03s ease-out;
}

.vis-network {
  height: 500px;
}
