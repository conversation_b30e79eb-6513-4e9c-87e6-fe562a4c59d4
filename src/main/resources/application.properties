quarkus.rest-client."com.sd.loms.infrastructure.planning.adapter.LomsPersistenceExternalService".url=http://localhost:8081
quarkus.rest-client."com.sd.loms.infrastructure.planning.adapter.LomsPersistenceExternalService".scope=jakarta.inject.Singleton
quarkus.live-reload.instrumentation=true
quarkus.timefold.solver.termination.unimproved-spent-limit=90s


quarkus.datasource.db-kind=sqlite
quarkus.datasource.jdbc.url=******************************************
quarkus.hibernate-orm.log.sql=true

quarkus.http.auth.proactive=false

# disable csrf validation for non form endpoints
quarkus.rest-csrf.require-form-url-encoded=false

quarkus.http.port=8081
quarkus.web-bundler.dependencies.node-modules=node_modules
quarkus.web-bundler.dependencies.compile-only=false
# quarkus.web-bundler.dependencies.auto-import=all
# quarkus.web-bundler.browser-live-reload=false


quarkus.locales=en,ja
quarkus.default-locale=en


mp.jwt.verify.publickey.location=publicKey.pem
mp.jwt.decrypt.key.location=privateKey.pem
smallrye.jwt.sign.key.location=privateKey.pem
smallrye.jwt.encrypt.key.location=publicKey.pem

quarkus.native.resources.includes=publicKey.pem,privateKey.pem


# Build time properties
quarkus.package.jar.enabled=false
quarkus.native.enabled=true
quarkus.native.container-build=true
quarkus.native.container-runtime-options=--platform=linux/amd64



# Application specific properties
navitime.customer.id=r2300730
navitime.signature=df2df09b909e6435ff95a71259372e9e2c0524710853681cc4dc8bf6e5bbe4f4
navitime.request.code=1qazxsw20909
navitime.tobe.loaded.from=localhost:8081
