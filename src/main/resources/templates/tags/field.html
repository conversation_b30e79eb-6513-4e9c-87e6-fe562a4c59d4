<div class="col-md-{cols ?: '12'}">
  <label for="{name}">{label}</label>
  <input id="${name}" name="{name}" placeholder="{label}" class="form-control{#if errors && errors.get(name)} is-invalid{/if}"
    {#if id??} id="{id}"{/if}{#if value} value="{value}"{/if}{#if type??} type="{type}"{/if}{#if focus??} autofocus{/if}>
  {#if errors && errors.get(name)}<div class="invalid-feedback">{errors.get(name)}</div>{/if}
</div>
