{#if validate == "date"}
  <div class="col-md-6 mb-3" id="runs">
    <label for="runId" class="form-label">実行: {runs.size}</label>
    <select id="runId" name="runId" class="form-select" up-validate="#solutions, #solution-details">
      <option value="0">選択してください</option>
      {#for run in runs}
        <option value="{run.id}">
          {run.id} - {run.status} - {run.fulfillDate}
        </option>
      {/for}
    </select>
  </div>
  <div class="col-md-12 mb-3" id="solutions">
    <label for="solutionId" class="form-label">配車結果: {solutions.size}</label>
    <select id="solutionId" name="solutionId" class="form-select" up-validate="#solution-details">
      <option value="0">選択してください</option>
      {#for solution in solutions}
        <option value="{solution.id}">
          {#if !solution.manual}
            {solution.readableTotalTime}, Score: {solution.score}
          {#else}
            「手動で変更されました」
          {/if}
        </option>
      {/for}
    </select>
  </div>
{/if}

{#if validate == "runId"}
  <div class="col-md-12 mb-3" id="solutions">
    <label for="solutionId" class="form-label">配車結果: {solutions.size}</label>
    <select id="solutionId" name="solutionId" class="form-select" up-validate="#solution-details">
      <option value="0">選択してください</option>
      {#for solution in solutions}
        <option value="{solution.id}">
          {#if !solution.manual}
            {solution.readableTotalTime}, Score: {solution.score}
          {#else}
            「手動で変更されました」
          {/if}
        </option>
      {/for}
    </select>
  </div>
{/if}
