<div class="card" id="run-details" 
     {#if solverInProgress}
       up-poll up-interval="2000" up-source="{uri:Solution.runDetails(date)}"
     {/if}>
  <div class="card-header">
    <div class="card-title">実行詳細</div>
  </div>
  <div class="card-body">
    {#if solverInProgress}
      <div class="d-flex align-items-center">
        <div class="spinner-border text-primary me-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <div>
          <h5 class="mb-1">ソルバー実行中...</h5>
          <p class="mb-0">配車計画を計算しています。しばらくお待ちください。</p>
        </div>
      </div>
    {#else}
      <div class="alert alert-success" role="alert">
        <h5 class="alert-heading">実行完了</h5>
        <p>配車計画の計算が完了しました。結果を確認してください。</p>
      </div>
    {/if}
  </div>
</div>
