{#include page}
  {#title}{m:allocation_page_heading}{/title}
  {#heading}{m:allocation_page_heading}{/heading}

  <div class="card-group">
    <div class="card">
      <div class="card-header">
        <div class="card-title">データを取得</div>
      </div>
      <div class="card-body">
        <form class="row g-3" up-history="auto">
          <div class="col-md-6 mb-3">
            <label for="date" class="form-label">日付</label>
            <input id="date" type="date" name="date" value="{date}" class="form-control" up-validate="#runs, #solutions, #solution-details"/>
          </div>
          <div class="col-md-6 mb-3" id="runs">
            <label for="runId" class="form-label">実行: {runs.size}</label>
            <select id="runId" name="runId" class="form-select" up-validate="#solutions, #solution-details">
              <option value="0">選択してください</option>
              {#for run in runs}
                <option value="{run.id}" {#if run.id == runId}selected{/if}>
                  {run.id} - {run.status} - {run.fulfillDate}
                </option>
              {/for}
            </select>
          </div>
          <div class="col-md-12 mb-3" id="solutions">
            <label for="solutionId" class="form-label">配車結果: {solutions.size}</label>
            <select id="solutionId" name="solutionId" class="form-select" up-validate="#solution-details">
              <option value="0">選択してください</option>
              {#for solution in solutions}
                <option value="{solution.id}" {#if solution.id == solutionId}selected{/if}>
                  {#if !solution.manual}
                    {solution.readableTotalTime}, Score: {solution.score}
                  {#else}
                    「手動で変更されました」
                  {/if}
                </option>
              {/for}
            </select>
          </div>
        </form>
      </div>
    </div>
    <div id="run-details" up-defer up-href="{uri:Solution.runDetails(date)}">
      Loading...
    </div>
  </div>

  <div class="row mt-5">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">Solution Details</div>
        </div>
        <div class="card-body">
          <div id="solution-details">
            {#if solutionId}
              <div id="actual-solution" up-defer up-href="{uri:Solution.solutionDetails(solutionId)}"></div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="map-container" up-defer up-href="{uri:Solution.map()}"></div>
{/include}
