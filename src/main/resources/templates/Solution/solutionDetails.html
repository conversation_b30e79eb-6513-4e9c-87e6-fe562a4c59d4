<div class="mt-4">
  <div class="card" id="solution-details">
    <div class="card-header">
      <div class="card-title">
        結果の詳細
        {#if solution.manuallyChanged}
          「手動で変更されました」
        {/if}
      </div>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <h5>配車情報</h5>
          <table class="table table-sm">
            <tbody>
              <tr>
                <td><strong>総時間:</strong></td>
                <td>{solution.readableTotalTime}</td>
              </tr>
              <tr>
                <td><strong>スコア:</strong></td>
                <td>{solution.score}</td>
              </tr>
              <tr>
                <td><strong>車両数:</strong></td>
                <td>{solution.vehicleRoutes.size}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="col-md-6">
          <h5>車両選択</h5>
          <div class="btn-group-vertical w-100" role="group">
            {#for route in solution.vehicleRoutes}
              <button type="button" class="btn btn-outline-primary" 
                      x-data="{selected: false}" 
                      @click="selected = !selected; $dispatch('vehicle-selected', {vehicleId: {route.vehicle.id}})">
                {route.vehicle.name} ({route.visits.size} 件)
              </button>
            {/for}
          </div>
        </div>
      </div>
      
      <div class="row mt-4">
        <div class="col-12">
          <h5>配送ルート詳細</h5>
          <div x-data="{selectedVehicle: null}" @vehicle-selected.window="selectedVehicle = $event.detail.vehicleId">
            {#for route in solution.vehicleRoutes}
              <div x-show="selectedVehicle == {route.vehicle.id}" class="mt-3">
                <h6 style="color: {route.vehicle.color}">{route.vehicle.name}</h6>
                <table class="table table-striped table-sm">
                  <thead>
                    <tr>
                      <th>順序</th>
                      <th>場所</th>
                      <th>タイプ</th>
                      <th>到着時間</th>
                      <th>出発時間</th>
                      <th>作業時間</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#for visit in route.visits}
                      <tr style="color: {route.vehicle.color}">
                        <td>{visit.visitIndex}</td>
                        <td>{visit.name}</td>
                        <td>{visit.type}</td>
                        <td>{visit.arrivalTime}</td>
                        <td>{visit.departureTime}</td>
                        <td>{visit.serviceTime}</td>
                      </tr>
                    {/for}
                  </tbody>
                </table>
              </div>
            {/for}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
