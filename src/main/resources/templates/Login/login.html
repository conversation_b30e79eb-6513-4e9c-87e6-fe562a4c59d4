{#include auth.html }
	{#title}{m:login}{/title}

	<div class="login-box">
		<div class="card">
			<form method="post" up-target="body" action={uri:Login.manualLogin()}>
			{#authenticityToken/}
			<div class="card-header">
				<h1 class="mb-0">
					<img src="/logo.png" alt="LOMS"/>
					<b>LOMS</b> ログイン
				</h1>
			</div>
			<div class="card-body login-card-body" up-main="root">
				{#field name = "userName" label = m:email errors = "" value = ""/}
				{#field name = "password" label = m:password errors = "" value = ""/}
			</div>
			<div class="card-footer">
				<div class="d-flex align-items-end justify-content-between">
					<a class="btn btn-outline-secondary d-grid gap-2" up-target=".card">ユーザー登録</a>
					<div class="d-grid gap-2">
						<button type="submit" class="btn btn-info">ログイン</button>
					</div>
				</div>
			</div>
			</form>
		</div>
	</div>

{/include}
