package com.sd.loms.presentation.controller


import io.quarkiverse.renarde.router.Router
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.core.Response


class Home : UpController() {

	@Path("/")
	fun index(): Response {
		val location = if (user == null) Router.getURI(Login::login) else Router.getURI(Home::index)
		return Response.seeOther(location).build()
	}

	@Path("/change_language/{lang}")
	fun changeLanguage(@PathParam("lang") lang: String): Response {
		i18n.set(lang)
		val message = i18n.formatMessage("language_changed", i18n.getMessage(lang))
		flash("refresh", message)
		if (!isUnpolyRequest()) index()
		return renderNothing()
	}

	@CheckedTemplate
	private object Templates {
		@JvmStatic
		external fun index(): TemplateInstance
	}
}
