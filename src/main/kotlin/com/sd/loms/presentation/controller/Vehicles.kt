package com.sd.loms.presentation.controller

import com.sd.loms.infrastructure.data.repo.VehicleRepository
import com.sd.loms.infrastructure.data.repo.ItemRepository
import com.sd.loms.infrastructure.data.repo.GroupRepository
import com.sd.loms.presentation.dto.VehicleForm
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import io.quarkus.security.Authenticated
import io.smallrye.common.annotation.Blocking
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.ws.rs.*
import org.jboss.resteasy.reactive.RestForm
import org.jboss.resteasy.reactive.RestQuery

@Authenticated
@Path("/vehicles")
class Vehicles(
    private val vehicleRepository: VehicleRepository,
    private val itemRepository: ItemRepository,
    private val groupRepository: GroupRepository
) : UpController() {

    @GET
    @Blocking
    fun index(@RestQuery page: Int = 1, @RestQuery size: Int = 20, @RestQuery sort: String = "id", @RestQuery order: String = "asc"): TemplateInstance {
        val vehicles = vehicleRepository.findAll().list()
        val vehicleForms = vehicles.map { VehicleForm.fromEntity(it) }
        return Templates.index(vehicleForms, page, size)
    }

    @GET
    @Path("/new")
    @Blocking
    fun addNew(): TemplateInstance {
        val groups = groupRepository.listAll()
        val items = itemRepository.listAll()
        return Templates.form(VehicleForm(), groups.map { it.toDomain() }, items.map { it.toDomain() }, "新規登録")
    }

    @GET
    @Path("/{id}")
    @Blocking
    fun details(@PathParam("id") id: Long): TemplateInstance {
        val vehicle = vehicleRepository.findById(id) ?: throw NotFoundException("Vehicle not found")
        val groups = groupRepository.listAll()
        val items = itemRepository.listAll()
        val vehicleForm = VehicleForm.fromEntity(vehicle)
        return Templates.form(vehicleForm, groups.map { it.toDomain() }, items.map { it.toDomain() }, "編集")
    }

    @POST
    @Transactional
    @Blocking
    fun save(@Valid @RestForm vehicleForm: VehicleForm): TemplateInstance {
        if (validationFailed()) {
            val groups = groupRepository.listAll()
            val items = itemRepository.listAll()
            return Templates.form(vehicleForm, groups.map { it.toDomain() }, items.map { it.toDomain() }, if (vehicleForm.id == null) "新規登録" else "編集")
        }

        val entity = vehicleForm.toEntity()
        if (vehicleForm.id == null) {
            vehicleRepository.persist(entity)
            flash("message", "車両を登録しました")
        } else {
            val existing = vehicleRepository.findById(vehicleForm.id!!) ?: throw NotFoundException("Vehicle not found")
            // Update existing entity fields
            existing.groupId = entity.groupId
            existing.serialNm = entity.serialNm
            existing.number = entity.number
            existing.numberPlate = entity.numberPlate
            existing.belongFlg = entity.belongFlg
            existing.vehicleType = entity.vehicleType
            existing.name = entity.name
            existing.model = entity.model
            existing.maximumLoadCapacity = entity.maximumLoadCapacity
            existing.frontTankCapacity = entity.frontTankCapacity
            existing.rearTankCapacity = entity.rearTankCapacity
            existing.totalTankCapacity = entity.totalTankCapacity
            existing.vehicleCategoryId = entity.vehicleCategoryId
            existing.weightCategoryId = entity.weightCategoryId
            existing.tankMaterialId = entity.tankMaterialId
            existing.alkaline = entity.alkaline
            existing.remarks = entity.remarks
            existing.displayOrder = entity.displayOrder
            vehicleRepository.persist(existing)
            flash("message", "車両を更新しました")
        }

        return renderNothing()
    }

    @DELETE
    @Path("/{id}")
    @Transactional
    @Blocking
    fun delete(@PathParam("id") id: Long): TemplateInstance {
        val vehicle = vehicleRepository.findById(id) ?: throw NotFoundException("Vehicle not found")
        vehicleRepository.delete(vehicle)
        flash("message", "車両を削除しました")
        return renderNothing()
    }

    @CheckedTemplate
    object Templates {
        @JvmStatic external fun index(vehicles: List<VehicleForm>, page: Int, size: Int): TemplateInstance
        @JvmStatic external fun form(vehicle: VehicleForm, groups: List<Any>, items: List<Any>, title: String): TemplateInstance
    }
}
