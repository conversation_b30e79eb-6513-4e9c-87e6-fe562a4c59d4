package com.sd.loms.presentation.controller

import com.sd.loms.infrastructure.data.repo.DriverRepository
import com.sd.loms.infrastructure.data.repo.ItemRepository
import com.sd.loms.infrastructure.data.repo.GroupRepository
import com.sd.loms.infrastructure.data.repo.VehicleRepository
import com.sd.loms.infrastructure.data.repo.SalaryRepository
import com.sd.loms.infrastructure.data.repo.LocationRepository
import com.sd.loms.presentation.dto.DriverForm
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import io.quarkus.security.Authenticated
import io.smallrye.common.annotation.Blocking
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.ws.rs.*
import org.jboss.resteasy.reactive.RestForm
import org.jboss.resteasy.reactive.RestQuery

@Authenticated
@Path("/drivers")
class Drivers(
    private val driverRepository: DriverRepository,
    private val itemRepository: ItemRepository,
    private val groupRepository: GroupRepository,
    private val vehicleRepository: VehicleRepository,
    private val salaryRepository: SalaryRepository,
    private val locationRepository: LocationRepository
) : UpController() {

    @GET
    @Blocking
    fun index(@RestQuery page: Int = 1, @RestQuery size: Int = 20, @RestQuery sort: String = "id", @RestQuery order: String = "asc"): TemplateInstance {
        val drivers = driverRepository.findAll().list()
        val driverForms = drivers.map { DriverForm.fromEntity(it) }
        return Templates.index(driverForms, page, size)
    }

    @GET
    @Path("/new")
    @Blocking
    fun addNew(): TemplateInstance {
        val groups = groupRepository.listAll()
        val items = itemRepository.listAll()
        val vehicles = vehicleRepository.listAll()
        val salaries = salaryRepository.listAll()
        val locations = locationRepository.listAll()
        return Templates.form(
            DriverForm(), 
            groups.map { it.toDomain() }, 
            items.map { it.toDomain() },
            vehicles.map { VehicleForm.fromEntity(it) },
            salaries,
            locations.map { it.toDomain() },
            "新規登録"
        )
    }

    @GET
    @Path("/{id}")
    @Blocking
    fun details(@PathParam("id") id: Long): TemplateInstance {
        val driver = driverRepository.findById(id) ?: throw NotFoundException("Driver not found")
        val groups = groupRepository.listAll()
        val items = itemRepository.listAll()
        val vehicles = vehicleRepository.listAll()
        val salaries = salaryRepository.listAll()
        val locations = locationRepository.listAll()
        val driverForm = DriverForm.fromEntity(driver)
        return Templates.form(
            driverForm, 
            groups.map { it.toDomain() }, 
            items.map { it.toDomain() },
            vehicles.map { VehicleForm.fromEntity(it) },
            salaries,
            locations.map { it.toDomain() },
            "編集"
        )
    }

    @POST
    @Transactional
    @Blocking
    fun save(@Valid @RestForm driverForm: DriverForm): TemplateInstance {
        if (validationFailed()) {
            val groups = groupRepository.listAll()
            val items = itemRepository.listAll()
            val vehicles = vehicleRepository.listAll()
            val salaries = salaryRepository.listAll()
            val locations = locationRepository.listAll()
            return Templates.form(
                driverForm, 
                groups.map { it.toDomain() }, 
                items.map { it.toDomain() },
                vehicles.map { VehicleForm.fromEntity(it) },
                salaries,
                locations.map { it.toDomain() },
                if (driverForm.id == null) "新規登録" else "編集"
            )
        }

        val entity = driverForm.toEntity()
        if (driverForm.id == null) {
            driverRepository.persist(entity)
            flash("message", "乗務員を登録しました")
        } else {
            val existing = driverRepository.findById(driverForm.id!!) ?: throw NotFoundException("Driver not found")
            // Update existing entity fields
            existing.employeeCd = entity.employeeCd
            existing.groupId = entity.groupId
            existing.nm1 = entity.nm1
            existing.nm2 = entity.nm2
            existing.kana2 = entity.kana2
            existing.snm = entity.snm
            existing.jobType = entity.jobType
            existing.belongFlg = entity.belongFlg
            existing.salaryId = entity.salaryId
            existing.keisu = entity.keisu
            existing.vehicleId = entity.vehicleId
            existing.locationId = entity.locationId
            existing.mobile = entity.mobile
            existing.cmtJob = entity.cmtJob
            existing.remarks = entity.remarks
            existing.employedDate = entity.employedDate
            existing.quitFlg = entity.quitFlg
            existing.quitDate = entity.quitDate
            driverRepository.persist(existing)
            flash("message", "乗務員を更新しました")
        }

        return renderNothing()
    }

    @DELETE
    @Path("/{id}")
    @Transactional
    @Blocking
    fun delete(@PathParam("id") id: Long): TemplateInstance {
        val driver = driverRepository.findById(id) ?: throw NotFoundException("Driver not found")
        driverRepository.delete(driver)
        flash("message", "乗務員を削除しました")
        return renderNothing()
    }

    @CheckedTemplate
    object Templates {
        @JvmStatic external fun index(drivers: List<DriverForm>, page: Int, size: Int): TemplateInstance
        @JvmStatic external fun form(driver: DriverForm, groups: List<Any>, items: List<Any>, vehicles: List<Any>, salaries: List<Any>, locations: List<Any>, title: String): TemplateInstance
    }
}
