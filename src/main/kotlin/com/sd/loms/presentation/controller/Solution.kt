package com.sd.loms.presentation.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.sd.loms.application.SolutionDataAccessUseCases
import com.sd.loms.application.SolutionUseCase
import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.SolutionVM
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import io.quarkus.security.Authenticated
import io.smallrye.common.annotation.Blocking
import jakarta.ws.rs.Path
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.jboss.resteasy.reactive.RestQuery
import java.time.LocalDate

@Authenticated
class Solution(
	private val solutionUseCase: SolutionUseCase,
	private val solutionDataAccess : SolutionDataAccessUseCases,
	private val objectMapper: ObjectMapper,

	@ConfigProperty(name = "navitime.customer.id") private val navitimeCustomerId: String,
	@ConfigProperty(name = "navitime.signature") private val navitimeSignature: String,
	@ConfigProperty(name = "navitime.request.code") private val navitimeRequestCode: String

) : UpController() {

	@Path("/solution")
	@Blocking
	fun index(@RestQuery date: LocalDate?, @RestQuery runId: Long? = 0, @RestQuery solutionId: Long? = 0): TemplateInstance {
		val unpolyValidate = up(UpRequestHeader.UP_VALIDATE)
		val selectedRunId = if (unpolyValidate == "date") 0L else (runId ?: 0)
		val dateSelected = date ?: LocalDate.now()
		val allocationRuns = solutionDataAccess.getAllocationRunsByDate(dateSelected)
		val solutions = if (selectedRunId != 0L) solutionDataAccess.findFeasibleSolutions(selectedRunId) else emptyList()
		val selectedSolutionId = if (unpolyValidate == "date" || unpolyValidate == "runId") 0L else (solutionId ?: 0)

		return Templates.index(dateSelected, selectedRunId, selectedSolutionId, allocationRuns, solutions)
	}

	@Path("/solution/validate")
	@Blocking
	fun validate(@RestQuery date: LocalDate?, @RestQuery runId: Long? = 0, @RestQuery solutionId: Long? = 0): TemplateInstance {
		val unpolyValidate = up(UpRequestHeader.UP_VALIDATE)
		val selectedRunId = if (unpolyValidate == "date") 0L else (runId ?: 0)
		val dateSelected = date ?: LocalDate.now()
		val allocationRuns = solutionUseCase.getAllocationRunsByDate(dateSelected)
		val solutions = if (selectedRunId != 0L) solutionUseCase.findFeasibleSolutions(selectedRunId) else emptyList()

		return Templates.validate(dateSelected, selectedRunId, allocationRuns, solutions, unpolyValidate ?: "")
	}

	@Path("/solution/run-details/{date}")
	@Blocking
	fun runDetails(@RestQuery date: LocalDate): TemplateInstance {
		// Implementation for run details
		return Templates.runDetails(date, false) // placeholder
	}

	@Path("/solution/solution-details/{solutionId}")
	@Blocking
	fun solutionDetails(@RestQuery solutionId: Long): TemplateInstance {
		// Implementation for solution details
		return Templates.solutionDetails(solutionId)
	}

	@Path("/solution/map")
	@Blocking
	fun map(): TemplateInstance {
		return Templates.map(navitimeCustomerId, navitimeSignature, navitimeRequestCode)
	}

	@CheckedTemplate
	object Templates {
		@JvmStatic external fun index(date: LocalDate, runId: Long?, solutionId: Long?,
									  runs: List<AllocationRun>, solutions: List<com.sd.loms.domain.Solution>): TemplateInstance
		@JvmStatic external fun validate(date: LocalDate, runId: Long?, runs: List<AllocationRun>,
										 solutions: List<com.sd.loms.domain.Solution>, validate: String): TemplateInstance
		@JvmStatic external fun runDetails(date: LocalDate, solverInProgress: Boolean): TemplateInstance
		@JvmStatic external fun solutionDetails(solutionId: Long): TemplateInstance
		@JvmStatic external fun map(customerId: String, signature: String, requestCode: String): TemplateInstance
	}

	@JvmRecord data class Run(val date: LocalDate, val solverInProgress: Boolean)                     : TemplateInstance
	@JvmRecord data class Map(val customerId: String, val signature: String, val requestCode: String) : TemplateInstance
	//@formatter:on
}
