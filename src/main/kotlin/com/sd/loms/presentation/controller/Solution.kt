package com.sd.loms.presentation.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.sd.loms.application.SolutionDataAccessUseCases
import com.sd.loms.application.SolutionUseCase
import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.SolutionVM
import io.quarkus.qute.TemplateInstance
import io.quarkus.security.Authenticated
import io.smallrye.common.annotation.Blocking
import jakarta.transaction.Transactional
import jakarta.validation.constraints.NotNull
import jakarta.ws.rs.POST
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.jboss.resteasy.reactive.RestForm
import org.jboss.resteasy.reactive.RestPath
import org.jboss.resteasy.reactive.RestQuery
import java.time.LocalDate

@Authenticated
class Solution(
	private val solutionUseCase: SolutionUseCase,
	private val solutionDataAccess : SolutionDataAccessUseCases,
	private val objectMapper: ObjectMapper,

	@ConfigProperty(name = "navitime.customer.id") private val navitimeCustomerId: String,
	@ConfigProperty(name = "navitime.signature") private val navitimeSignature: String,
	@ConfigProperty(name = "navitime.request.code") private val navitimeRequestCode: String

) : UpController() {

	@Blocking
	fun index(@RestQuery date: LocalDate?, @RestQuery runId: Long? = 0, @RestQuery solutionId: Long? = 0): TemplateInstance {
		val unpolyValidate = up(UpRequestHeader.UP_VALIDATE)
		val selectedRunId = if (unpolyValidate == "date") 0L else (runId ?: 0)
		val dateSelected = date ?: LocalDate.now()
		val allocationRuns = solutionDataAccess.getAllocationRunsByDate(dateSelected)
		val solutions = if (selectedRunId != 0L) solutionDataAccess.findFeasibleSolutions(selectedRunId) else emptyList()
		val selectedSolutionId = if (unpolyValidate == "date" || unpolyValidate == "runId") 0L else (solutionId ?: 0)
		if(selectedSolutionId != 0L) {
			return solution(selectedSolutionId)
		}

		return Index(dateSelected, selectedRunId, selectedSolutionId, allocationRuns, solutions)
	}

	@Blocking
	fun solution(@RestPath solutionId: Long): TemplateInstance {
		val solution = solutionDataAccess.findBySolutionId(solutionId)
		val data = objectMapper.writeValueAsString(solution.vehicleRoutes)
		val solutions = if (solution.runId != 0L) solutionDataAccess.findFeasibleSolutions(solution.runId) else emptyList()
		return Solution(data, solution, null, true, solutions)
	}

	fun map() = Map(navitimeCustomerId, navitimeSignature, navitimeRequestCode)

	fun runDetails(@RestQuery date: LocalDate?): TemplateInstance {
		val solverInProgress = solutionDataAccess.isThereAnySolverInProgress()
		return Run(date ?: LocalDate.now(), solverInProgress)
	}

	@POST
	@Transactional
	@Blocking
	fun solve(@NotNull @RestForm date: LocalDate, @RestForm withHistory: Boolean, @RestForm withEvenAllocation: Boolean): TemplateInstance {
		val problem = solutionDataAccess.getProblemInput(date, withHistory, withEvenAllocation)
		val result = solutionUseCase.solveAndListen(problem)
		result.fold(
			onSuccess = { flashMessage(false) },
			onFailure = { flashMessage(true) }
		)
		return runDetails(date)
	}

	private fun flashMessage(noJobsFound: Boolean) {
		val messageKey = if (noJobsFound) "no_jobs_found" else "solution_started"
		flash(if (noJobsFound) "info" else "message", i18n.formatMessage(messageKey, i18n.getMessage("solution")))
	}

	//@formatter:off
	@JvmRecord data class Index(val date: LocalDate,           val runId: Long?,         val solutionId: Long?,
								val runs: List<AllocationRun>, val solutions: List<com.sd.loms.domain.Solution>,
	)                                                                                                 : TemplateInstance

	@JvmRecord data class Solution(val data: String,         val solution: SolutionVM,    val vehicle: Long?,
								   val isInclude: Boolean,   val solutions: List<com.sd.loms.domain.Solution>
	)                                                                                                 : TemplateInstance

	@JvmRecord data class Run(val date: LocalDate, val solverInProgress: Boolean)                     : TemplateInstance
	@JvmRecord data class Map(val customerId: String, val signature: String, val requestCode: String) : TemplateInstance
	//@formatter:on
}
