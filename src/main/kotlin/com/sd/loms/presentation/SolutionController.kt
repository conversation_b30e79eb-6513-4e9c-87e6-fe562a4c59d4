package com.sd.loms.presentation

import com.sd.loms.application.SolutionUseCase
import com.sd.loms.domain.SolutionChangeValidationRequest
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.QueryParam
import java.time.LocalDate

@Path("/api/v1/solutions")
class SolutionController(private val solutionUseCase: SolutionUseCase) {

	@POST
	@Path("/solve")
	fun solve(
		@QueryParam("date") date: LocalDate,
		@QueryParam("withHistory") withHistory: Boolean = false,
		@QueryParam("withEvenAllocation") withEvenAllocation: Boolean = false
	) = solutionUseCase.solveAndListen(date, withHistory, withEvenAllocation)

	@POST
	@Path("/validate-solution-change")
	fun validateSolutionChange(validationRequest: SolutionChangeValidationRequest) = solutionUseCase.validateSolutionChange(validationRequest)

}
