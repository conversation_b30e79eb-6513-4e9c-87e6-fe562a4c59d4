package com.sd.loms.presentation.dto

import com.sd.loms.domain.DriverDto
import com.sd.loms.infrastructure.data.entity.DriverEntity
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.LocalDate

data class DriverForm(
    val id: Long? = null,

    val employeeCd: String? = null,

    @field:NotNull(message = "グループを選択してください")
    val groupId: Long? = null,

    @field:NotBlank(message = "氏名を入力してください")
    val nm1: String? = null,

    val nm2: String? = null,

    val kana2: String? = null,

    val snm: String? = null,

    val jobType: Int? = null,

    val belongFlg: Boolean = true,

    val salaryId: Long? = null,

    val keisu: Double? = null,

    val vehicleId: Long? = null,

    @field:NotNull(message = "所在地を選択してください")
    val locationId: Long? = null,

    val mobile: String? = null,

    val cmtJob: String? = null,

    val remarks: String? = null,

    val employedDate: LocalDate? = null,

    val quitFlg: Boolean = false,

    val quitDate: LocalDate? = null,

    val opt1: String? = null,

    val opt2: String? = null,

    val opt3: String? = null,

    val romsId: Long? = null,

    val materialCategoryIds: List<Long> = emptyList(),

    val vehicleIds: List<Long> = emptyList(),

    val canHandleNames: String = "",

    val name: String = ""
) {

    fun toDomain(): DriverDto {
        return DriverDto(
            id = id ?: 0L,
            name = nm1 ?: nm2 ?: snm ?: "",
            primaryVehicle = vehicleId ?: 0L,
            canHandle = materialCategoryIds.toSet(),
            canDrive = vehicleIds.toSet()
        )
    }

    fun toEntity(): DriverEntity {
        val entity = DriverEntity()
        entity.id = id
        entity.employeeCd = employeeCd
        entity.groupId = groupId ?: 0L
        entity.nm1 = nm1
        entity.nm2 = nm2
        entity.kana2 = kana2
        entity.snm = snm
        entity.jobType = jobType
        entity.belongFlg = belongFlg
        entity.salaryId = salaryId
        entity.keisu = keisu
        entity.vehicleId = vehicleId
        entity.locationId = locationId ?: 0L
        entity.mobile = mobile
        entity.cmtJob = cmtJob
        entity.remarks = remarks
        entity.employedDate = employedDate
        entity.quitFlg = quitFlg
        entity.quitDate = quitDate
        entity.opt1 = opt1
        entity.opt2 = opt2
        entity.opt3 = opt3
        entity.romsId = romsId
        entity.createdAt = System.currentTimeMillis()
        return entity
    }

    companion object {
        fun fromDomain(driver: DriverDto): DriverForm {
            return DriverForm(
                id = driver.id,
                nm1 = driver.name,
                vehicleId = driver.primaryVehicle,
                materialCategoryIds = driver.canHandle.toList(),
                vehicleIds = driver.canDrive.toList(),
                canHandleNames = "", // Will be populated from relationships
                name = driver.name
            )
        }

        fun fromEntity(entity: DriverEntity): DriverForm {
            return DriverForm(
                id = entity.id,
                employeeCd = entity.employeeCd,
                groupId = entity.groupId,
                nm1 = entity.nm1,
                nm2 = entity.nm2,
                kana2 = entity.kana2,
                snm = entity.snm,
                jobType = entity.jobType,
                belongFlg = entity.belongFlg,
                salaryId = entity.salaryId,
                keisu = entity.keisu,
                vehicleId = entity.vehicleId,
                locationId = entity.locationId,
                mobile = entity.mobile,
                cmtJob = entity.cmtJob,
                remarks = entity.remarks,
                employedDate = entity.employedDate,
                quitFlg = entity.quitFlg,
                quitDate = entity.quitDate,
                opt1 = entity.opt1,
                opt2 = entity.opt2,
                opt3 = entity.opt3,
                romsId = entity.romsId,
                materialCategoryIds = entity.materialCategories.map { it.id!! },
                vehicleIds = entity.vehicles.map { it.id!! },
                canHandleNames = entity.materialCategories.joinToString(", ") { it.name },
                name = entity.nm1 ?: entity.nm2 ?: entity.snm ?: ""
            )
        }
    }
}
