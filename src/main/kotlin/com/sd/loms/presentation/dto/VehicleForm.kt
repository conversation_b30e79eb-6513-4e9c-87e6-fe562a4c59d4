package com.sd.loms.presentation.dto

import com.sd.loms.infrastructure.data.entity.VehicleEntity
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

data class VehicleForm(
    val id: Long? = null,
    
    @field:NotNull(message = "グループを選択してください")
    val groupId: Long? = null,
    
    val serialNm: String? = null,
    
    val number: String? = null,
    
    val numberPlate: String? = null,
    
    val belongFlg: Boolean = true,
    
    val vehicleType: Int? = null,
    
    val tankType: String? = null,
    
    val tankLimit1: String? = null,
    
    val tankLimit2: String? = null,
    
    val totalLimit: String? = null,
    
    @field:NotBlank(message = "車両名を入力してください")
    val name: String? = null,
    
    val model: String? = null,
    
    val initialYm: String? = null,
    
    val inspectYm: String? = null,
    
    val remarks: String? = null,
    
    val tankSize1: String? = null,
    
    val tankSize2: String? = null,
    
    val gas: String? = null,
    
    val displayOrder: Int? = null,
    
    val vehicleCategoryId: Long? = null,
    
    val weightCategoryId: Long? = null,
    
    val tankMaterialId: Long? = null,
    
    val alkaline: Long? = null,
    
    val maximumLoadCapacity: Int? = null,
    
    val frontTankCapacity: Int? = null,
    
    val rearTankCapacity: Int? = null,
    
    val totalTankCapacity: Int? = null,
    
    val romsId: Long? = null,
    
    val materialCategoryIds: List<Long> = emptyList(),
    
    val canHandleNames: String = ""
) {
    
    fun toEntity(): VehicleEntity {
        val entity = VehicleEntity()
        entity.id = id
        entity.groupId = groupId ?: 0L
        entity.serialNm = serialNm
        entity.number = number
        entity.numberPlate = numberPlate
        entity.belongFlg = belongFlg
        entity.vehicleType = vehicleType
        entity.tankType = tankType
        entity.tankLimit1 = tankLimit1
        entity.tankLimit2 = tankLimit2
        entity.totalLimit = totalLimit
        entity.name = name
        entity.model = model
        entity.initialYm = initialYm
        entity.inspectYm = inspectYm
        entity.remarks = remarks
        entity.tankSize1 = tankSize1
        entity.tankSize2 = tankSize2
        entity.gas = gas
        entity.displayOrder = displayOrder
        entity.vehicleCategoryId = vehicleCategoryId
        entity.weightCategoryId = weightCategoryId
        entity.tankMaterialId = tankMaterialId
        entity.alkaline = alkaline
        entity.maximumLoadCapacity = maximumLoadCapacity
        entity.frontTankCapacity = frontTankCapacity
        entity.rearTankCapacity = rearTankCapacity
        entity.totalTankCapacity = totalTankCapacity
        entity.romsId = romsId
        entity.createdAt = System.currentTimeMillis()
        return entity
    }
    
    companion object {
        fun fromEntity(entity: VehicleEntity): VehicleForm {
            return VehicleForm(
                id = entity.id,
                groupId = entity.groupId,
                serialNm = entity.serialNm,
                number = entity.number,
                numberPlate = entity.numberPlate,
                belongFlg = entity.belongFlg,
                vehicleType = entity.vehicleType,
                tankType = entity.tankType,
                tankLimit1 = entity.tankLimit1,
                tankLimit2 = entity.tankLimit2,
                totalLimit = entity.totalLimit,
                name = entity.name,
                model = entity.model,
                initialYm = entity.initialYm,
                inspectYm = entity.inspectYm,
                remarks = entity.remarks,
                tankSize1 = entity.tankSize1,
                tankSize2 = entity.tankSize2,
                gas = entity.gas,
                displayOrder = entity.displayOrder,
                vehicleCategoryId = entity.vehicleCategoryId,
                weightCategoryId = entity.weightCategoryId,
                tankMaterialId = entity.tankMaterialId,
                alkaline = entity.alkaline,
                maximumLoadCapacity = entity.maximumLoadCapacity,
                frontTankCapacity = entity.frontTankCapacity,
                rearTankCapacity = entity.rearTankCapacity,
                totalTankCapacity = entity.totalTankCapacity,
                romsId = entity.romsId,
                materialCategoryIds = entity.materialCategories.map { it.id!! },
                canHandleNames = entity.materialCategories.joinToString(", ") { it.name }
            )
        }
    }
}
