package com.sd.loms.migration


data class MPref(val name: String, val sort: Int, val romsId: Int, val deletedAt: String?)

data class VehicleCategory(val name: String, val sort: Int, val deletedAt: String? = null)
data class VehicleWeightCategory(val name: String, val sort: Int, val deletedAt: String? = null)
data class VehicleTankMaterial(val name: String, val sort: Int, val deletedAt: String? = null)
data class VehicleAlkaline(val name: String, val sort: Int, val deletedAt: String? = null)

data class MItem(
    val name: String,
    val snm1: String?,
    val snm2: String?,
    val category: String?,
    val rolly1Flg: Boolean,
    val rolly2Flg: Boolean,
    val rolly3Flg: Boolean,
    val bcolor: String?,
    val fcolor: String?,
    val sort: Int,
    val opt2: String?,
    val opt3: String?,
    val poisonFlg: Boolean,
    val romsId: Int,
    val deletedAt: String?
)

data class MLocation(
    val latLng: String,
    val zip: String?,
    val prefId: Int?,
    val cityCd: String?,
    val add1: String?,
    val add1Kana: String?,
    val add2: String?,
    val add2Kana: String?,
    val add3: String?,
    val add3Kana: String?,
    val tel: String?,
    val fax: String?,
    val uniqueId: String,
    val type: String?,
    val typeId: Long?,
    val deletedAt: String?
)

data class MGroup(
    val nm: String,
    val groupNm: String?,
    val groupKana: String?,
    val groupType: String?,
    val itemCat: String?,
    val locationId: String,
    val opt1: String?,
    val opt2: String?,
    val gNo: Int,
    val deletedAt: String?
)

data class MPickup(
    val nm: String,
    val kana: String?,
    val snm1: String?,
    val snm2: String?,
    val locationId: String,
    val startTime: String?,
    val lunchTime: String?,
    val endTime: String?,
    val opt1: String?,
    val opt2: String?,
    val opt3: String?,
    val romsId: Int,
    val deletedAt: String?
)

data class MPickupGroup(val pickupId: Int, val groupId: Int)

data class Shipper(
    val chargeCd: String?,
    val nm: String?,
    val kana: String?,
    val snm1: String?,
    val snm2: String?,
    val locationId: String,
    val opt1: String?,
    val opt2: String?,
    val opt3: String?,
    val opt4: String?,
    val opt5: String?,
    val romsId: Int,
    val deletedAt: String?
)

data class MDestCompany(
    val name: String,
    val kana: String?,
    val opt1: String?,
    val opt2: String?,
    val opt3: String?,
    val romsId: Int,
    val deletedAt: String?
)

data class MDestArea(
    val destCompanyId: Int?,
    val serialNo: Int?,
    val nm: String?,
    val kana: String?,
    val opt1: String?,
    val opt2: String?,
    val opt3: String?,
    val locationId: String,
    val romsId: Int,
    val deletedAt: String?
)

data class MDestSpot(
    val destAreaId: Int,
    val nm: String?,
    val spot: String?,
    val itemId: Int?,
    val kana1: String?,
    val kana2: String?,
    val snm1: String?,
    val snm2: String?,
    val dept1: String?,
    val dept2: String?,
    val person1: String?,
    val person2: String?,
    val contact1: String?,
    val contact2: String?,
    val cmt1Flg: Boolean,
    val cmt1: String?,
    val cmt2Flg: Boolean,
    val cmt2: String?,
    val cmt3Flg: Boolean,
    val cmt3: String?,
    val cmt4Flg: Boolean,
    val cmt4: String?,
    val cmt5Flg: Boolean,
    val cmt5: String?,
    val cmt6Flg: Boolean,
    val cmt6: String?,
    val cmt7Flg: Boolean,
    val cmt7: String?,
    val cmt8Flg: Boolean,
    val cmt8: String?,
    val cmt9Flg: Boolean,
    val cmt9: String?,
    val cmt10Flg: Boolean,
    val cmt10: String?,
    val fileId: String?,
    val file1: String?,
    val file2: String?,
    val remarks: String?,
    val romsId: Int,
    val deletedAt: String?
)

data class MDestGroup(
    val destSpotId: Int,
    val groupId: Int,
    val destCd: String,
    val shipperId: Int,
    val keisu: String?,
    val fareType: String?,
    val fareDist: String?,
    val farePrice: String?,
    val remarks: String?,
    val romsId: Int,
    val deletedAt: String?
)

data class MDistance(
    val fromLocId: String,
    val toLocId: String,
    val duration: Int
)


data class Order(
    val typeNo: Int,
    val importKey: String?,
    val destCd: String,
    val itemId: Int,
    val pickupId: Int,
    val volume1: Int?,
    val unit1: String?,
    val volume2: Int?,
    val unit2: String?,
    val requirement: String?,
    val shipDate: String,
    val deliDate: String,
    val setTime: String?,
    val cmt1: String?,
    val cmt2: String?,
    val remarks: String?,
    val orderStatus: String,
    val pNo: String?,
    val opt1: String?,
    val opt2: String?,
    val opt3: String?,
    val romsId: String,
    val lockUId: String?,
    val deletedAt: String?
)

data class MVehicle(
    val groupId: Int,
    val serialNm: String?,
    val number: String?,
    val numberPlate: String?,
    val belongFlg: Boolean,
    val vehicleType: String?,
    val tankType: String?,
    val tankLimit1: String?,
    val tankLimit2: String?,
    val totalLimit: String?,
    val name: String?,
    val model: String?,
    val initialYm: String?,
    val inspectYm: String?,
    val remarks: String?,
    val tankSize1: String?,
    val tankSize2: String?,
    val gas: String?,
    val sort: Int,
    val romsId: Int,
    val deletedAt: String?
)

data class VehicleMaterialCategory(
    val vehicleId: Int,
    val itemId: Int,
)

data class EntityMapping(val entityId: Int, val mappingId: Int)

data class VehicleTypeAndCapacity(
    val romsId: Int,

    val category: Int,
    val weightCategory: Int,
    val tankMaterial: Int,
    val alkaline: Int,
    val frontTankCapacity: Int,
    val rearTankCapacity: Int,
    val totalCapacity: Int,
    val maximumLoadCapacity: Int,
)

data class MUser(
    val name: String,
    val email: String,
    val password: String,
    val verified: Boolean,
    val deletedAt: String?
)

data class MSalary(
    val cls: String,
    val rank: String,
    val base: Int,
    val g1: Boolean,
    val g2: Boolean,
    val g3: Boolean,
    val g4: Boolean,
    val g5: Boolean,
    val romsId: Int,
    val deletedAt: String?
)

data class MDriver(
    val employeeCd: String?,
    val groupId: Int,
    val nm1: String?,
    val nm2: String?,
    val kana2: String?,
    val snm: String?,
    val jobType: Int?,
    val belongFlg: Boolean?,
    val salaryId: Int?,
    val keisu: Double?,
    val vehicleId: Int?,
    val locationId: String,
    val mobile: String?,
    val cmtJob: String?,
    val remarks: String?,
    val employedDate: String?,
    val quitFlg: Boolean?,
    val quitDate: String?,
    val opt1: String?,
    val opt2: String?,
    val opt3: String?,
    val romsId: Int?,
    val deletedAt: String?
)

data class Pattern (
    val shipperId: String,
    val pickupId: String,
    val destinationId: String,
    val itemId: String,
    val vehicleId: String,
    val driver: String,
)