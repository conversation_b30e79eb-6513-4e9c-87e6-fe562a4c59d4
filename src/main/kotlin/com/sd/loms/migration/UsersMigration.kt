package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class UsersMigration(
    private val google: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = "select name, email, password, verified, deleted_at from m_users;"

        val insertSql = "insert into m_users(name, email, password, verified, deleted_at) values (:name, :email, :password, :verified, :deletedAt);"

        val items = google.query(selectSql, SFM.newInstance().newRowMapper(MUser::class.java))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MUser::class.java).newSqlParameterSources(items)
        destination.batchUpdate(insertSql, batch)
        return items
    }
}


