package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DriversMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: Map<String, Int>,
    private val groups: Map<Int, Int>,
    private val salaries: Map<Int, Int>,
    private val vehicles: Map<Int, Int>
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select employee_cd, g_no groupId, nm1, nm2, kana2, snm, job_type, belong_flg,
                   salary_cd salary_id, keisu, cast(vehicle_cd as integer) vehicle_id, concat('m_drivers_', cast(driver_cd as integer)) location_id,
                   mobile, cmt_job, remarks, employed_date, quit_flg, quit_date, opt1, opt2, opt3,
                   cast(driver_cd as integer) roms_id, case when delete_flg = true then current_timestamp else null end deleted_at
            from m_drivers
            order by cast(driver_cd as integer);
        """.trimIndent()
        val drivers = source.query(selectSql, SFM.newInstance().newRowMapper(MDriver::class.java))
            .map {
                it.copy(
                    locationId = locations[it.locationId].toString(),
                    groupId = groups[it.groupId] ?: error("Group not found for ${it.groupId}"),
                    salaryId = it.salaryId?.let { salaries[it] ?: error("Salary not found for $it") },
                    vehicleId = it.vehicleId?.let { vehicles[it] ?: error("Vehicle not found for $it") },
                )
            }

        val insertSql = """
            insert into m_drivers (
                employee_cd, group_id, nm1, nm2, kana2, snm, job_type, belong_flg, salary_id, keisu, 
                vehicle_id, location_id, mobile, cmt_job, remarks, employed_date, quit_flg, 
                quit_date, opt1, opt2, opt3, roms_id, deleted_at
            ) VALUES (
                :employeeCd, :groupId, :nm1, :nm2, :kana2, :snm, :jobType, :belongFlg, :salaryId, 
                :keisu, :vehicleId, :locationId, :mobile, :cmtJob, :remarks, :employedDate, 
                :quitFlg, :quitDate, :opt1, :opt2, :opt3, :romsId, :deletedAt
            )
        """.trimIndent()
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDriver::class.java).newSqlParameterSources(drivers)
        destination.batchUpdate(insertSql, batch)
        return drivers
    }
}
