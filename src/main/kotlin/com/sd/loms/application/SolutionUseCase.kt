package com.sd.loms.application

import com.sd.loms.domain.AllocationProblem
import com.sd.loms.domain.SolutionChangeValidationRequest
import com.sd.loms.domain.ports.SolutionPort
import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDate

@ApplicationScoped
class SolutionUseCase(private val solutionPort: SolutionPort) {
	fun solveAndListen(date: LocalDate, withHistory: <PERSON>ole<PERSON>, withEvenAllocation: <PERSON>olean) = solutionPort.solveAndListen(date, withHistory, withEvenAllocation)
	fun validateSolutionChange(validationRequest: SolutionChangeValidationRequest) = solutionPort.validateSolutionChange(validationRequest)
}
