package com.sd.loms.application

import com.sd.loms.domain.ports.SolutionDataAccessPort
import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDate

@ApplicationScoped
class SolutionDataAccessUseCases(private val solutionPort: SolutionDataAccessPort) {

	fun getAllocationRunsByDate(date: LocalDate) = solutionPort.getAllocationRunsByDate(date)
	fun findFeasibleSolutions(runId: Long) = solutionPort.findFeasibleSolutions(runId)
	fun findBySolutionId(solutionId: Long) = solutionPort.findBySolutionId(solutionId)
	fun isThereAnySolverInProgress() = solutionPort.isThereAnySolverInProgress()
	fun getProblemInput(date: LocalDate, withHistory: Boolean, withEvenAllocation: Boolean) = solutionPort.getProblemInput(date, withHistory, withEvenAllocation)
}
