package com.sd.loms.application

import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.ports.SolutionDataAccessPort
import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDate

@ApplicationScoped
class SolutionDataAccessUseCases(private val solutionPort: SolutionDataAccessPort) {

	fun getAllocationRunsByDate(date: LocalDate) = solutionPort.getAllocationRunsByDate(date)
}
