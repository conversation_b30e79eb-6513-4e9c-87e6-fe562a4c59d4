package com.sd.loms.domain.ports

import com.sd.loms.domain.DriverDto
import com.sd.loms.domain.Item
import com.sd.loms.domain.Vehicle

interface DriverDataAccessPort {
    fun getAllDrivers(): List<DriverDto>
    fun getDriverById(id: Long): DriverDto?
    fun saveDriver(driver: DriverDto): Long
    fun updateDriver(driver: DriverDto): DriverDto
    fun deleteDriver(id: Long)
    fun getAllGroups(): List<Any> // Using Any for now, should be proper Group domain object
    fun getAllItems(): List<Item>
    fun getAllVehicles(): List<Vehicle>
    fun getAllSalaries(): List<Any> // Using Any for now, should be proper Salary domain object
    fun getAllLocations(): List<Any> // Using Any for now, should be proper Location domain object
}
