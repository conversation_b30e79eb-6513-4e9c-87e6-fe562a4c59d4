package com.sd.loms.domain.ports

import com.sd.loms.domain.AllocationProblem
import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.SolutionChangeValidationRequest
import com.sd.loms.domain.SolutionChangeValidationResponse
import java.time.LocalDate

interface SolutionPort {
	fun solveAndListen(problem: AllocationProblem): Long
	fun validateSolutionChange(request: SolutionChangeValidationRequest): SolutionChangeValidationResponse


	fun getAllocationRunsByDate(date: LocalDate): List<AllocationRun>
}
