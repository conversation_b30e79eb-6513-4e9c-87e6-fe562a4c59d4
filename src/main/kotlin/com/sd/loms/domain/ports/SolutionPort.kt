package com.sd.loms.domain.ports

import com.sd.loms.domain.SolutionChangeValidationRequest
import com.sd.loms.domain.SolutionChangeValidationResponse
import java.time.LocalDate

interface SolutionPort {
	fun solveAndListen(date: LocalDate, withHistory: <PERSON>olean, withEvenAllocation: <PERSON>olean): Result<Long>
	fun validateSolutionChange(request: SolutionChangeValidationRequest): SolutionChangeValidationResponse
}
