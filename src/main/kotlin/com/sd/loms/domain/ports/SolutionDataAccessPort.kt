package com.sd.loms.domain.ports

import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.Solution
import com.sd.loms.domain.SolutionVM
import java.time.LocalDate

interface SolutionDataAccessPort {
	fun getAllocationRunsByDate(date: LocalDate): List<AllocationRun>
	fun findFeasibleSolutions(runId: Long): List<Solution>
	fun findBySolutionId(solutionId: Long): SolutionVM
	fun isThereAnySolverInProgress(): Boolean
}
