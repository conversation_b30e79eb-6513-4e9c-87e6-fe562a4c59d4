package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.auth.DomainUser
import com.sd.loms.domain.ports.AuthPort
import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.repo.UserRepository
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional

@ApplicationScoped
@Transactional
@EnableSoftDeleteFilter
class AuthAdapter(private val userRepository: UserRepository) : AuthPort {
	override fun findRegisteredByEmail(email: String): DomainUser? {
		userRepository.findRegisteredByName(email)?.let {
			return DomainUser(it.email, it.password, it.name, it.isAdmin, it.status)
		}
		return null
	}

	override fun findByUserName(userName: String): DomainUser? {
		userRepository.findByName(userName)?.let {
			return DomainUser(it.email, it.password, it.name, it.isAdmin, it.status)
		}
		return null
	}

}
