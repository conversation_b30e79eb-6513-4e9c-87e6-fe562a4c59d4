package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_items", indexes = [
	Index(name = "idx_m_items_category", columnList = "category"),
	Index(name = "idx_m_items_rolly_flags", columnList = "rolly1_flg, rolly2_flg, rolly3_flg"),
	Index(name = "idx_m_items_deleted_at", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class ItemEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(nullable = false)
	lateinit var name: String

	var snm1: String? = null

	var snm2: String? = null

	var category: Int? = null

	@Column(name = "rolly2_flg")
	var rolly2Flg: Boolean = false

	@Column(name = "rolly1_flg")
	var rolly1Flg: Boolean = false

	@Column(name = "rolly3_flg")
	var rolly3Flg: Boolean = false

	var bcolor: String? = null

	var fcolor: String? = null

	var sort: Int? = null

	var opt2: String? = null

	var opt3: String? = null

	@Column(name = "poison_flg", nullable = false)
	var poisonFlg: Boolean = false

	@Column(name = "cleaning_flg", nullable = false)
	var cleaningFlg: Boolean = false

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@OneToMany(mappedBy = "item", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var destSpots: MutableList<DestSpotEntity> = mutableListOf()

	@OneToMany(mappedBy = "item", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var orders: MutableList<OrderEntity> = mutableListOf()

	@ManyToMany(mappedBy = "materialCategories", fetch = FetchType.LAZY)
	var drivers: MutableList<DriverEntity> = mutableListOf()

	@ManyToMany(mappedBy = "materialCategories", fetch = FetchType.LAZY)
	var vehicles: MutableList<VehicleEntity> = mutableListOf()
}
