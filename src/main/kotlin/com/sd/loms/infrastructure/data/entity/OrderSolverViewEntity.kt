package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Immutable
import java.time.LocalDate

/**
 * Immutable entity representing the v_orders_4_solver view.
 * This view provides comprehensive order data for the solver optimization engine.
 */
@Entity
@Immutable
@Table(name = "v_orders_4_solver")
data class OrderSolverViewEntity(
    
    @Id
    @Column(name = "id")
    val id: Long,
    
    @Column(name = "fulfill_date")
    val fulfillDateString: String,
    
    @Column(name = "set_time")
    val setTime: String?,
    
    @Column(name = "volume1")
    val volume1: String?,
    
    @Column(name = "unit1")
    val unit1: Int?,
    
    @Column(name = "volume2")
    val volume2: String?,
    
    @Column(name = "unit2")
    val unit2: Int?,
    
    @Column(name = "pickup_id")
    val pickupId: Long,
    
    @Column(name = "pickup_location_id")
    val pickupLocationId: Long,
    
    @Column(name = "pickup_location_unique_id")
    val pickupLocationUniqueId: String,
    
    @Column(name = "pickup_location_name")
    val pickupLocationName: String,
    
    @Column(name = "pickup_location_short_name")
    val pickupLocationShortName: String?,
    
    @Column(name = "pickup_location_lat")
    val pickupLocationLat: String,
    
    @Column(name = "pickup_location_lon")
    val pickupLocationLon: String,
    
    @Column(name = "pickup_location_address")
    val pickupLocationAddress: String,
    
    @Column(name = "destination_cd")
    val destinationCd: String,
    
    @Column(name = "destination_id")
    val destinationId: Long,
    
    @Column(name = "destination_location_id")
    val destinationLocationId: Long,
    
    @Column(name = "destination_location_unique_id")
    val destinationLocationUniqueId: String,
    
    @Column(name = "destination_location_name")
    val destinationLocationName: String,
    
    @Column(name = "destination_location_short_name")
    val destinationLocationShortName: String,
    
    @Column(name = "destination_location_lat")
    val destinationLocationLat: String,
    
    @Column(name = "destination_location_lon")
    val destinationLocationLon: String,
    
    @Column(name = "destination_location_address")
    val destinationLocationAddress: String,
    
    @Column(name = "shipper_id")
    val shipperId: Long,
    
    @Column(name = "shipper_name")
    val shipperName: String,
    
    @Column(name = "item_id")
    val itemId: Long,
    
    @Column(name = "item_name")
    val itemName: String,
    
    @Column(name = "item_cleaning_flag")
    val itemCleaningFlag: Int,
    
    @Column(name = "deleted_at")
    val deletedAt: String?,
    
    @Column(name = "group_id")
    val groupId: Long
    
) {
    
    /**
     * Computed property to convert string fulfill date to LocalDate
     */
    val fulfillDate: LocalDate
        get() = LocalDate.parse(fulfillDateString)
    
    /**
     * Computed property to parse pickup location latitude as BigDecimal
     */
    val pickupLocationLatDecimal: java.math.BigDecimal
        get() = pickupLocationLat.toBigDecimalOrNull() ?: java.math.BigDecimal.ZERO
    
    /**
     * Computed property to parse pickup location longitude as BigDecimal
     */
    val pickupLocationLonDecimal: java.math.BigDecimal
        get() = pickupLocationLon.toBigDecimalOrNull() ?: java.math.BigDecimal.ZERO
    
    /**
     * Computed property to parse destination location latitude as BigDecimal
     */
    val destinationLocationLatDecimal: java.math.BigDecimal
        get() = destinationLocationLat.toBigDecimalOrNull() ?: java.math.BigDecimal.ZERO
    
    /**
     * Computed property to parse destination location longitude as BigDecimal
     */
    val destinationLocationLonDecimal: java.math.BigDecimal
        get() = destinationLocationLon.toBigDecimalOrNull() ?: java.math.BigDecimal.ZERO
    
    /**
     * Computed property to parse volume1 as integer
     */
    val volume1Int: Int
        get() = volume1?.toIntOrNull() ?: 0
    
    /**
     * Computed property to parse volume2 as integer
     */
    val volume2Int: Int
        get() = volume2?.toIntOrNull() ?: 0
    
    /**
     * Computed property to check if item requires cleaning
     */
    val requiresCleaning: Boolean
        get() = itemCleaningFlag == 1
}
