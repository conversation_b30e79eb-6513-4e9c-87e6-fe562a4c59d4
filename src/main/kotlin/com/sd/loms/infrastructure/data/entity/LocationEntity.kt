package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_locations")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class LocationEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "lat_lng", nullable = false)
	lateinit var latLng: String

	var zip: String? = null

	@Column(name = "pref_id")
	var prefId: Long? = null

	@Column(name = "city_cd")
	var cityCd: String? = null

	var add1: String? = null

	@Column(name = "add1_kana")
	var add1Kana: String? = null

	var add2: String? = null

	@Column(name = "add2_kana")
	var add2Kana: String? = null

	var add3: String? = null

	@Column(name = "add3_kana")
	var add3Kana: String? = null

	var tel: String? = null

	var fax: String? = null

	@Column(name = "unique_id")
	var uniqueId: String? = null

	var type: String? = null

	@Column(name = "type_id")
	var typeId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "pref_id", insertable = false, updatable = false)
	var pref: PrefEntity? = null

	@OneToMany(mappedBy = "location", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var groups: MutableList<GroupEntity> = mutableListOf()

	@OneToMany(mappedBy = "location", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var shippers: MutableList<ShipperEntity> = mutableListOf()

	@OneToMany(mappedBy = "location", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var pickups: MutableList<PickupEntity> = mutableListOf()

	@OneToMany(mappedBy = "location", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var destAreas: MutableList<DestAreaEntity> = mutableListOf()

	@OneToMany(mappedBy = "location", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var drivers: MutableList<DriverEntity> = mutableListOf()

	@OneToMany(mappedBy = "fromLocation", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var distancesFrom: MutableList<DistanceEntity> = mutableListOf()

	@OneToMany(mappedBy = "toLocation", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var distancesTo: MutableList<DistanceEntity> = mutableListOf()
}
