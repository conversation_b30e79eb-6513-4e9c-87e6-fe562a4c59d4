package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*

@Entity
@Table(name = "t_solution_details")
class SolutionDetailsEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "solution_id", nullable = false)
	var solutionId: Long = 0

	@Column(name = "vehicle_id", nullable = false)
	var vehicleId: Long = 0

	@Column(name = "driver_id", nullable = false)
	var driverId: Long = 0

	@Column(name = "order_id", nullable = false)
	lateinit var orderId: String

	@Column(name = "visit_index", nullable = false)
	var visitIndex: Int = 0

	@Column(name = "location_unique_id", nullable = false)
	lateinit var locationUniqueId: String

	@Column(name = "arrival_time", nullable = false)
	var arrivalTime: Int = 0

	@Column(name = "service_time", nullable = false)
	var serviceTime: Int = 0

	@Column(name = "departure_time", nullable = false)
	var departureTime: Int = 0

	@Column(name = "time_to_depot")
	var timeToDepot: Int? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "solution_id", insertable = false, updatable = false)
	var solution: SolutionEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "vehicle_id", insertable = false, updatable = false)
	var vehicle: VehicleEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "driver_id", insertable = false, updatable = false)
	var driver: DriverEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "order_id", referencedColumnName = "id", insertable = false, updatable = false)
	var order: OrderEntity? = null
}
