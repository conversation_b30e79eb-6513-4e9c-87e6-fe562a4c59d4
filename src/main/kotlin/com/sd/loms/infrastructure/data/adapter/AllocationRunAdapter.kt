package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.ports.AllocationRunPort
import com.sd.loms.infrastructure.data.entity.AllocationRunEntity
import com.sd.loms.infrastructure.data.repo.AllocationRunRepository
import jakarta.enterprise.context.ApplicationScoped

@ApplicationScoped
class AllocationRunAdapter(private val allocationRunRepo: AllocationRunRepository): AllocationRunPort {
	override fun save(allocationRun: AllocationRun): AllocationRun {
		val entity = AllocationRunEntity.fromDomain(allocationRun)
		allocationRunRepo.persist(entity)
		return entity.toDomain()
	}

	override fun update(allocationRun: AllocationRun): AllocationRun {
		val entity = AllocationRunEntity.fromDomain(allocationRun)
		allocationRunRepo.persist(entity)
		return entity.toDomain()
	}
}
