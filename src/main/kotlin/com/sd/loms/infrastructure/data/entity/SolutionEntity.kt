package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "t_solutions")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class SolutionEntity {


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "run_id", nullable = false)
	var runId: Long = 0

	@Column(name = "total_time")
	var totalTime: Int? = null

	var score: String? = null

	@Column(name = "is_feasible")
	var isFeasible: Int? = null

	@Column(name = "is_manual")
	var isManual: Int? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "run_id", insertable = false, updatable = false)
	var allocationRun: AllocationRunEntity? = null

	@OneToMany(mappedBy = "solution", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var solutionDetails: MutableList<SolutionDetailsEntity> = mutableListOf()

	fun toDomain() = com.sd.loms.domain.Solution(
		id = id,
		runId = runId,
		totalTime = totalTime?.toLong() ?: 0L,
		score = score ?: "",
		feasible = isFeasible ?: 0,
		solutionDetails = solutionDetails.map { it.toDomain() },
		manual = (isManual ?: 0) == 1,
		oldSolutionId = 0L // Not available in entity
	)
}
