package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.Cacheable
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef

@Entity
@Table(name = "t_solutions")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class SolutionEntity {


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null


	var runId: Long = 0
	var totalTime: Long? = null
	var score: String? = null
	var feasible: Int? = null
	var manual: Boolean? = null
	var oldSolutionId: Long? = null
	var createdAt: Long? = null
	var createdBy: Long? = null
	var updatedAt: Long? = null
	var updatedBy: Long? = null
	var deletedAt: Long? = null
}
