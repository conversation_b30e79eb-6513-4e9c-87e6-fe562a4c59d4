package com.sd.loms.infrastructure.data.entity

import com.sd.loms.domain.auth.UserStatus
import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate


@Entity
@Table(name = "m_users")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class UserEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(nullable = false, unique = true)
	lateinit var email: String

	@Column(nullable = false)
	lateinit var name: String

	@Column(nullable = false)
	lateinit var password: String

	@Column(nullable = false)
	var isAdmin: Boolean = false

	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	lateinit var status: UserStatus

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@OneToMany(mappedBy = "user", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var passwordTokens: MutableList<PasswordTokenEntity> = mutableListOf()

	fun toDomain() = com.sd.loms.domain.auth.DomainUser(
		email = email,
		password = password,
		userName = name,
		isAdmin = isAdmin,
		status = status
	)
}
