package com.sd.loms.infrastructure.data.entity

import com.sd.loms.domain.auth.UserStatus
import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate


@Entity
@Table(name = "m_users")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class UserEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Int? = null

	@Column(nullable = false, unique = true)
	lateinit var email: String

	@Column(nullable = false, unique = true)
	lateinit var name: String
	lateinit var password: String
	var isAdmin: Boolean = false

	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	lateinit var status: UserStatus


	var createdAt: Long = 0
	var createdBy: Long = 0
	var updatedAt: Long = 0
	var updatedBy: Long = 0
	var deletedAt: LocalDate? = null

}
