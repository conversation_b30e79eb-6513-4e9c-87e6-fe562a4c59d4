package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "t_orders", indexes = [
	Index(name = "t_order_idx1", columnList = "dest_cd"),
	Index(name = "t_order_idx2", columnList = "item_id"),
	Index(name = "t_order_idx3", columnList = "pickup_id"),
	Index(name = "t_order_idx4", columnList = "deli_date"),
	Index(name = "t_order_idx5", columnList = "p_no"),
	Index(name = "t_order_idx6", columnList = "deleted_at"),
	Index(name = "t_order_idx7", columnList = "type_no")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class OrderEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "roms_id", nullable = false)
	lateinit var romsId: String

	@Column(name = "type_no")
	var typeNo: Int? = null

	@Column(name = "import_key")
	var importKey: String? = null

	@Column(name = "dest_cd", nullable = false)
	lateinit var destCd: String

	@Column(name = "item_id", nullable = false)
	var itemId: Long = 0

	@Column(name = "pickup_id", nullable = false)
	var pickupId: Long = 0

	@Column(nullable = false)
	lateinit var volume1: String

	@Column(nullable = false)
	lateinit var unit1: String

	var volume2: String? = null

	var unit2: Int? = null

	var requirement: String? = null

	@Column(name = "ship_date", nullable = false)
	lateinit var shipDate: LocalDate

	@Column(name = "deli_date", nullable = false)
	lateinit var deliDate: LocalDate

	@Column(name = "set_time")
	var setTime: String? = null

	var cmt1: String? = null

	var cmt2: String? = null

	var remarks: String? = null

	@Column(name = "order_status")
	var orderStatus: Short = 1

	@Column(name = "p_no")
	var pNo: Int? = null

	var opt1: String? = null

	var opt2: String? = null

	var opt3: String? = null

	@Column(name = "lock_u_id")
	var lockUId: String? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "pickup_id", insertable = false, updatable = false)
	var pickup: PickupEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "dest_cd", referencedColumnName = "dest_cd", insertable = false, updatable = false)
	var destGroup: DestGroupEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "item_id", insertable = false, updatable = false)
	var item: ItemEntity? = null

	@OneToMany(mappedBy = "order", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var solutionDetails: MutableList<SolutionDetailsEntity> = mutableListOf()

	fun toDomain() = com.sd.loms.domain.OrderDto(
		id = id!!,
		pickupId = pickupId,
		deliveryId = 0L, // Would need to be derived from destGroup
		itemId = itemId,
		shipperId = 0L, // Would need to be derived from destGroup
		fulfillDate = deliDate,
		weight = volume1.toIntOrNull() ?: 0,
		litres = volume2?.toIntOrNull() ?: 0
	)
}
