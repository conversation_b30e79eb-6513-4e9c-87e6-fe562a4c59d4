package com.sd.loms.infrastructure.data.repo

import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.OrderSolverViewEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import jakarta.persistence.EntityManager
import java.time.LocalDate

/**
 * Repository for accessing the v_orders_4_solver view.
 * This repository provides methods to query order data optimized for the solver.
 */
@ApplicationScoped
@EnableSoftDeleteFilter
class OrderSolverViewRepository : PanacheRepository<OrderSolverViewEntity> {

    /**
     * Find all orders by fulfill date for solver optimization.
     * Uses string comparison to handle SQLite date format issues.
     */
    fun findAllByFulfillDate(date: LocalDate): List<OrderSolverViewEntity> {
        return list("fulfillDateString = ?1", date.toString())
    }

    /**
     * Find all orders by fulfill date and group ID.
     * Useful for filtering orders by specific groups (e.g., Honsha group).
     */
    fun findAllByFulfillDateAndGroupId(date: LocalDate, groupId: Long): List<OrderSolverViewEntity> {
        return list("fulfillDateString = ?1 AND groupId = ?2", date.toString(), groupId)
    }

    /**
     * Find all orders by date range for solver optimization.
     * Useful for multi-day optimization scenarios.
     */
    fun findAllByFulfillDateBetween(startDate: LocalDate, endDate: LocalDate): List<OrderSolverViewEntity> {
        return list("fulfillDateString >= ?1 AND fulfillDateString <= ?2", startDate.toString(), endDate.toString())
    }

    /**
     * Find all orders by shipper ID and fulfill date.
     * Useful for shipper-specific optimization.
     */
    fun findAllByShipperIdAndFulfillDate(shipperId: Long, date: LocalDate): List<OrderSolverViewEntity> {
        return list("shipperId = ?1 AND fulfillDateString = ?2", shipperId, date.toString())
    }

    /**
     * Find all orders by item ID and fulfill date.
     * Useful for material-specific optimization.
     */
    fun findAllByItemIdAndFulfillDate(itemId: Long, date: LocalDate): List<OrderSolverViewEntity> {
        return list("itemId = ?1 AND fulfillDateString = ?2", itemId, date.toString())
    }

    /**
     * Find all orders that require cleaning by fulfill date.
     * Useful for identifying orders that need special handling.
     */
    fun findAllByCleaningRequiredAndFulfillDate(date: LocalDate): List<OrderSolverViewEntity> {
        return list("itemCleaningFlag = 1 AND fulfillDateString = ?1", date.toString())
    }

    /**
     * Find all orders by pickup location ID and fulfill date.
     * Useful for location-specific optimization.
     */
    fun findAllByPickupLocationIdAndFulfillDate(pickupLocationId: Long, date: LocalDate): List<OrderSolverViewEntity> {
        return list("pickupLocationId = ?1 AND fulfillDateString = ?2", pickupLocationId, date.toString())
    }

    /**
     * Find all orders by destination location ID and fulfill date.
     * Useful for destination-specific optimization.
     */
    fun findAllByDestinationLocationIdAndFulfillDate(destinationLocationId: Long, date: LocalDate): List<OrderSolverViewEntity> {
        return list("destinationLocationId = ?1 AND fulfillDateString = ?2", destinationLocationId, date.toString())
    }

    /**
     * Get unique pickup location IDs for a given fulfill date.
     * Useful for building location lists for distance calculations.
     */
    fun getUniquePickupLocationIds(date: LocalDate): List<Long> {
        return getEntityManager()
            .createQuery(
                "SELECT DISTINCT o.pickupLocationId FROM OrderSolverViewEntity o WHERE o.fulfillDateString = :date",
                Long::class.java
            )
            .setParameter("date", date.toString())
            .resultList
    }

    /**
     * Get unique destination location IDs for a given fulfill date.
     * Useful for building location lists for distance calculations.
     */
    fun getUniqueDestinationLocationIds(date: LocalDate): List<Long> {
        return getEntityManager()
            .createQuery(
                "SELECT DISTINCT o.destinationLocationId FROM OrderSolverViewEntity o WHERE o.fulfillDateString = :date",
                Long::class.java
            )
            .setParameter("date", date.toString())
            .resultList
    }

    /**
     * Get all unique location IDs (both pickup and destination) for a given fulfill date.
     * Useful for comprehensive distance matrix calculations.
     */
    fun getAllUniqueLocationIds(date: LocalDate): List<Long> {
        val pickupIds = getUniquePickupLocationIds(date)
        val destinationIds = getUniqueDestinationLocationIds(date)
        return (pickupIds + destinationIds).distinct()
    }

    /**
     * Get unique item IDs for a given fulfill date.
     * Useful for building item/material category lists.
     */
    fun getUniqueItemIds(date: LocalDate): List<Long> {
        return getEntityManager()
            .createQuery(
                "SELECT DISTINCT o.itemId FROM OrderSolverViewEntity o WHERE o.fulfillDateString = :date",
                Long::class.java
            )
            .setParameter("date", date.toString())
            .resultList
    }

    /**
     * Get unique shipper IDs for a given fulfill date.
     * Useful for shipper-based optimization scenarios.
     */
    fun getUniqueShipperIds(date: LocalDate): List<Long> {
        return getEntityManager()
            .createQuery(
                "SELECT DISTINCT o.shipperId FROM OrderSolverViewEntity o WHERE o.fulfillDateString = :date",
                Long::class.java
            )
            .setParameter("date", date.toString())
            .resultList
    }

    /**
     * Count orders by fulfill date.
     * Useful for optimization planning and resource allocation.
     */
    fun countByFulfillDate(date: LocalDate): Long {
        return count("fulfillDateString = ?1", date.toString())
    }

    /**
     * Debug method to get all available fulfill dates.
     * Useful for troubleshooting and data exploration.
     */
    fun getAllAvailableFulfillDates(): List<String> {
        return getEntityManager()
            .createQuery(
                "SELECT DISTINCT o.fulfillDateString FROM OrderSolverViewEntity o ORDER BY o.fulfillDateString",
                String::class.java
            )
            .resultList
    }
}
