package com.sd.loms.infrastructure.data.repo

import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.OrderEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDate

@ApplicationScoped
@EnableSoftDeleteFilter
class OrderRepository : PanacheRepository<OrderEntity> {
	fun findAllByFulfillDate(date: LocalDate): List<OrderEntity> {
		// Try multiple approaches for SQLite compatibility

		// Approach 1: Direct LocalDate comparison (should work with proper JPA setup)
		var result = list("deliDate = ?1", date)
		if (result.isNotEmpty()) return result

		// Approach 2: String comparison with ISO format
		result = list("deliDate = ?1", date.toString())
		if (result.isNotEmpty()) return result

		// Approach 3: Use DATE function for SQLite
		result = list("DATE(deliDate) = DATE(?1)", date.toString())
		if (result.isNotEmpty()) return result

		// Approach 4: Use date range (most compatible)
		result = list("deliDate >= ?1 AND deliDate < ?2", date, date.plusDays(1))
		if (result.isNotEmpty()) return result

		// Approach 5: Use string pattern matching as last resort
		return list("CAST(deliDate AS TEXT) LIKE ?1", "${date}%")
	}

	// Debug methods to help troubleshoot
	fun findAllDatesForDebugging(): List<String> {
		// Use native query to avoid LocalDate parsing issues
		return getEntityManager()
			.createNativeQuery("SELECT DISTINCT deli_date FROM t_orders WHERE deli_date IS NOT NULL ORDER BY deli_date")
			.resultList
			.map { it.toString() }
	}

	fun findByDateString(dateString: String) = list("deliDate = ?1", dateString)

	fun countAllOrders() = count()

	// Additional debug method to see raw data
	fun findAllOrdersRaw(): List<kotlin.collections.Map<String, Any?>> {
		val query = getEntityManager()
			.createNativeQuery("SELECT id, deli_date, pickup_id, item_id FROM t_orders LIMIT 10")

		return query.resultList.map { row ->
			val array = row as Array<*>
			mapOf(
				"id" to array[0],
				"deli_date" to array[1],
				"pickup_id" to array[2],
				"item_id" to array[3]
			)
		}
	}
}
