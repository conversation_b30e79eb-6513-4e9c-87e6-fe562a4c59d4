package com.sd.loms.infrastructure.data.repo

import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.OrderEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import jakarta.persistence.EntityManager
import java.time.LocalDate

@ApplicationScoped
@EnableSoftDeleteFilter
class OrderRepository : PanacheRepository<OrderEntity> {
	fun findAllByFulfillDate(date: LocalDate): List<OrderEntity> {
		// Query using the string field to avoid SQLite date parsing issues
		val params = date.toString()
		return list("deliDateString = ?1 and destGroup.groupId = 1", params)
	}

	// Debug methods to help troubleshoot
	fun findAllDatesForDebugging(): List<String> {
		// Use the string field to avoid parsing issues
		return list("1=1").map { it.deliDateString }.distinct().sorted()
	}

	fun findByDateString(dateString: String) = list("deliDateString = ?1 and destGroup.groupId = 1", dateString)

	fun countAllOrders() = count()

	// Additional debug method to see raw data
	fun findAllOrdersRaw(): List<kotlin.collections.Map<String, Any?>> {
		return list("1=1").take(10).map { order ->
			mapOf(
				"id" to order.id,
				"deli_date" to order.deliDateString,
				"deli_date_parsed" to order.deliDate,
				"pickup_id" to order.pickupId,
				"item_id" to order.itemId
			)
		}
	}
}
