package com.sd.loms.infrastructure.data.repo

import com.sd.loms.domain.auth.UserStatus
import com.sd.loms.infrastructure.data.entity.UserEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import java.util.Locale

@ApplicationScoped
class UserRepository : PanacheRepository<UserEntity> {

	fun findUnconfirmedByEmail(email: String) = find(
		"LOWER(email) = ?1 AND status = ?2",
		email.lowercase(Locale.getDefault()),
		UserStatus.CONFIRMATION_REQUIRED
	).firstResult()

	fun findRegisteredByName(email: String): UserEntity? {
		return find(
			"LOWER(email) = ?1 AND status = ?2",
			email.lowercase(Locale.getDefault()),
			UserStatus.REGISTERED
		).firstResult()
	}

	fun findByName(name: String) = find("LOWER(name) = ?1", name.lowercase(Locale.getDefault())).firstResult()

	fun findForConfirmation(confirmationCode: String) = find(
		"confirmationCode = ?1 AND status = ?2",
		confirmationCode,
		UserStatus.CONFIRMATION_REQUIRED
	).firstResult()
}
