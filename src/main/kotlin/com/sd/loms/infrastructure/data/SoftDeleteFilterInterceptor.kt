package com.sd.loms.infrastructure.data

import jakarta.interceptor.AroundInvoke
import jakarta.interceptor.Interceptor
import jakarta.interceptor.InterceptorBinding
import jakarta.interceptor.InvocationContext
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import jakarta.transaction.Transactional
import org.hibernate.Session
import java.io.Serializable

@InterceptorBinding
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.FUNCTION, AnnotationTarget.CLASS)
annotation class EnableSoftDeleteFilter

@EnableSoftDeleteFilter
@Interceptor
class SoftDeleteFilterInterceptor : Serializable {

	@PersistenceContext
	lateinit var em: EntityManager

	@AroundInvoke
	fun intercept(ctx: InvocationContext): Any {
		em.unwrap(Session::class.java)
			.enableFilter("softDeleteFilter")
		return ctx.proceed()
	}
}

