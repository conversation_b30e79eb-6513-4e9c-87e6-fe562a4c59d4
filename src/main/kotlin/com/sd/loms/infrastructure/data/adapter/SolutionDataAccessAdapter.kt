package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.Solution
import com.sd.loms.domain.ports.SolutionDataAccessPort
import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.repo.AllocationRunRepository
import com.sd.loms.infrastructure.data.repo.SolutionRepository
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import java.time.LocalDate

@ApplicationScoped
@Transactional
@EnableSoftDeleteFilter
class SolutionDataAccessAdapter(
	private val allocationRunRepo: AllocationRunRepository,
		private val solutionRepo: SolutionRepository
) : SolutionDataAccessPort {
	override fun getAllocationRunsByDate(date: LocalDate) =
		allocationRunRepo.list("fulfillDate = :date", date).map { it.toDomain() }

	override fun findFeasibleSolutions(runId: Long) =
		solutionRepo.list("runId = :runId", runId).map { it.toDomain() }


}
