package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.*
import com.sd.loms.domain.ports.SolutionDataAccessPort
import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.AllocationRunEntity
import com.sd.loms.infrastructure.data.entity.SolutionEntity
import com.sd.loms.infrastructure.data.repo.*
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import java.time.LocalDate

@ApplicationScoped
@Transactional
@EnableSoftDeleteFilter
class SolutionDataAccessAdapter(
	private val allocationRunRepo: AllocationRunRepository,
	private val solutionRepo: SolutionRepository,
	private val vehicleRepo: VehicleRepository,
	private val driverRepo: DriverRepository,
	private val orderRepo: OrderRepository,
	private val locationRepo: LocationRepository,
	private val itemRepo: ItemRepository
) : SolutionDataAccessPort {
	override fun getAllocationRunsByDate(date: LocalDate): List<AllocationRun> {
		val runs = allocationRunRepo.list("fulfillDate = ?1", date).map { it.toDomain() }
		return runs
	}

	override fun findFeasibleSolutions(runId: Long) =
		solutionRepo.list("runId = ?1", runId).map { it.toDomain() }

	override fun findBySolutionId(solutionId: Long): SolutionVM {
		val solution = solutionRepo.findById(solutionId)
		val allocationRun = allocationRunRepo.findById(solution?.runId ?: 0L)
		return solutionVM(allocationRun, solution)
	}

	override fun isThereAnySolverInProgress(): Boolean {
		return allocationRunRepo.count("status = ?1", RunStatus.SOLVING) > 0
	}

	private fun solutionVM(allocationRun: AllocationRunEntity?, solution: SolutionEntity?): SolutionVM {
		if (allocationRun == null || solution == null) {
			return SolutionVM(
				runId = 0,
				solutionId = 0,
				totalTime = 0,
				solverStatus = "NOT_FOUND",
				vehicleRoutes = emptyList(),
				vehicles = emptyList()
			)
		}

		// Get all vehicles used in the solution
		val vehicleIds = solution.solutionDetails.map { it.vehicleId }.distinct()
		val vehicles = vehicleRepo.list("id IN ?1", vehicleIds).map { it.toDomain() }

		// Group solution details by vehicle to create routes
		val vehicleRoutes = solution.solutionDetails
			.groupBy { it.vehicleId }
			.map { (vehicleId, details) ->
				val vehicle = vehicles.find { it.id == vehicleId }
				val driver = driverRepo.findById(details.first().driverId)

				// Sort visits by visit index
				val sortedDetails = details.sortedBy { it.visitIndex }

				// Create visits from solution details
				val visits = sortedDetails.map { detail ->
					val location = locationRepo.findById(detail.locationUniqueId.toLongOrNull() ?: 0L)
					val order = orderRepo.findById(detail.orderId.toLongOrNull() ?: 0L)
					val item = order?.let { itemRepo.findById(it.itemId) }

					// Determine visit type based on location or order data
					val visitType = when {
						location?.type == "DEPOT" -> VisitType.DEPOT
						detail.locationUniqueId.contains("pickup", ignoreCase = true) -> VisitType.PICKUP
						else -> VisitType.DELIVERY
					}

					VisitVM(
						id = detail.orderId.toLongOrNull() ?: 0L,
						uniqueId = detail.locationUniqueId,
						jobId = detail.orderId.toLongOrNull() ?: 0L,
						type = visitType,
						visitIndex = detail.visitIndex,
						item = item?.name ?: "",
						arrivalTime = detail.arrivalTime.toLong(),
						serviceDuration = detail.serviceTime.toLong(),
						departureTime = detail.departureTime.toLong(),
						distanceToDepot = detail.timeToDepot?.toLong(),
						distanceFromPreviousVisit = null // Would need to be calculated
					)
				}

				VehicleRouteVM(
					vehicle = VehicleVM(
						id = vehicle?.id ?: vehicleId,
						driverId = driver?.id ?: 0L,
						canHandle = vehicle?.canHandle?.map { it.name }?.toSet() ?: emptySet(),
						time = sortedDetails.sumOf { it.departureTime.toLong() - it.arrivalTime.toLong() }
					),
					visits = visits
				)
			}

		return SolutionVM(
			runId = allocationRun.id,
			solutionId = solution.id!!,
			totalTime = solution.totalTime!!,
			solverStatus = allocationRun.status.name,
			vehicleRoutes = vehicleRoutes,
			vehicles = vehicles
		)
	}
}
