package com.sd.loms.infrastructure.data.entity

import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.RunStatus
import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate


@Entity
@Table(name = "t_allocation_runs")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class AllocationRunEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long = 0

	@Column(name = "fulfill_date", nullable = false)
	lateinit var fulfillDate: LocalDate

	@Enumerated(EnumType.ORDINAL)
	var status: RunStatus = RunStatus.SOLVING

	@Column(name = "with_history", nullable = false)
	var withHistory: Boolean = false

	@Column(name = "with_even_allocation", nullable = false)
	var withEvenAllocation: Boolean = false

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@OneToMany(mappedBy = "allocationRun", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var solutions: MutableList<SolutionEntity> = mutableListOf()

	fun toDomain() = AllocationRun(id, status, fulfillDate, withHistory, withEvenAllocation)
}
