package com.sd.loms.infrastructure.data.entity

import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.RunStatus
import jakarta.persistence.Cacheable
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate


@Entity
@Table(name = "t_allocation_runs")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class AllocationRunEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long = 0

	@Column(nullable = false)
	@Enumerated(EnumType.ORDINAL)
	var status: RunStatus = RunStatus.SOLVING
	lateinit var fulfillDate: LocalDate
	var withHistory: Boolean = false
	var withEvenAllocation: Boolean = false
	var createdAt: Long = 0
	var createdBy: Long = 0
	var updatedAt: Long = 0
	var updatedBy: Long = 0
	var deletedAt: LocalDate? = null


	fun toDomain() = AllocationRun(id, status, fulfillDate, withHistory, withEvenAllocation)
}
