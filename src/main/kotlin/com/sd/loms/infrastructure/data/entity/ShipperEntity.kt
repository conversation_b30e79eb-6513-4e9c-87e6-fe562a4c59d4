package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_shipper", indexes = [
	Index(name = "idx_m_shipper_deleted_at", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class ShipperEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "charge_cd", nullable = false)
	lateinit var chargeCd: String

	@Column(nullable = false)
	lateinit var nm: String

	var kana: String? = null

	var snm1: String? = null

	var snm2: String? = null

	@Column(name = "location_id", nullable = false)
	var locationId: Long = 0

	var opt1: String? = null

	var opt2: String? = null

	var opt3: String? = null

	var opt4: String? = null

	var opt5: String? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "location_id", insertable = false, updatable = false)
	var location: LocationEntity? = null

	@OneToMany(mappedBy = "shipper", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var destGroups: MutableList<DestGroupEntity> = mutableListOf()
}
