package services

import (
	"cmp"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"loms/framework/log"
	"loms/pkg/services/api"
	"loms/pkg/services/repo"
	"loms/pkg/types"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
)

type SolutionService struct {
	JobStore           repo.JobStore
	VehicleStore       repo.VehicleStore
	DriverStore        repo.DriverStore
	LocationStore      repo.LocationStore
	DistanceStore      repo.DistanceStore
	PatternStore       repo.PatternStore
	solutionStore      repo.SolutionStore
	AllocationRunStore repo.AllocationRunStore
	solver             *api.SolverApi
}

func NewSolutionService(
	jobStore repo.JobStore,
	vehicleStore repo.VehicleStore,
	driverStore repo.DriverStore,
	locationStore repo.LocationStore,
	distanceStore repo.DistanceStore,
	patternStore repo.PatternStore,
	solutionStore repo.SolutionStore,
	allocationRunStore repo.AllocationRunStore,
	solver *api.SolverApi,
) *SolutionService {
	return &SolutionService{
		JobStore:           jobStore,
		VehicleStore:       vehicleStore,
		DriverStore:        driverStore,
		LocationStore:      locationStore,
		DistanceStore:      distanceStore,
		PatternStore:       patternStore,
		solutionStore:      solutionStore,
		AllocationRunStore: allocationRunStore,
		solver:             solver,
	}
}

func (s *SolutionService) Save(ctx context.Context, input *types.SolutionSaveInput) (*types.SolutionEntity, error) {

	solutionDetails := make([]*types.SolutionDetailsEntity, 0)
	for _, sd := range input.SolutionDetails {
		solutionDetails = append(solutionDetails, &types.SolutionDetailsEntity{
			SolutionID:       input.ID,
			VehicleID:        sd.VehicleID,
			DriverID:         sd.DriverID,
			OrderID:          sd.OrderID,
			VisitIndex:       sd.VisitIndex,
			LocationUniqueID: sd.UniqueID,
			ArrivalTime:      sd.ArrivalTime,
			ServiceTime:      sd.ServiceTime,
			DepartureTime:    sd.DepartureTime,
			TimeToDepot:      sd.TimeToDepot,
		})
	}
	solution := &types.SolutionEntity{
		RunID:     input.RunID,
		TotalTime: input.TotalTime,
		CreatedAt: time.Now().Unix(),

		Score:      input.Score,
		IsFeasible: input.Feasible,
		Run:        nil,
		IsManual:   input.Manual,
	}

	if input.Manual {
		oldSolution, err := s.solutionStore.FindById(ctx, input.OldSolutionId)
		if err != nil {
			return nil, err
		}
		oldSolutionDetails := lo.Filter(oldSolution.SolutionDetails, func(sd *types.SolutionDetailsEntity, _ int) bool {
			return !lo.ContainsBy(solutionDetails, func(newSd *types.SolutionDetailsEntity) bool {
				return newSd.VehicleID == sd.VehicleID && newSd.DriverID == sd.DriverID
			})
		})
		oldSolutionDetails = lo.Map(oldSolutionDetails, func(sd *types.SolutionDetailsEntity, _ int) *types.SolutionDetailsEntity {
			sd.SolutionID = input.ID
			sd.ID = 0
			return sd
		})
		solution.SolutionDetails = append(solutionDetails, oldSolutionDetails...)

		if oldSolution.IsManual {
			err := s.solutionStore.Delete(ctx, oldSolution.ID)
			if err != nil {
				return nil, err
			}
		}
	} else {
		solution.SolutionDetails = solutionDetails
	}
	return s.solutionStore.Save(ctx, solution)
}

func (s *SolutionService) FindAllFeasible(ctx context.Context, runId int64) ([]types.SolutionDropDownData, error) {
	solutions, err := s.solutionStore.FindAllFeasible(ctx, runId)

	if err != nil {
		return nil, err
	}

	startTime := solutions[0].Run.CreatedAt
	return lo.Map(solutions, func(s types.SolutionEntity, _ int) types.SolutionDropDownData {
		timeDiff := s.CreatedAt - startTime
		return types.SolutionDropDownData{
			ID:           s.ID,
			TotalTime:    s.TotalTime,
			Score:        s.Score,
			TimeToCreate: timeDiff,
			IsManual:     s.IsManual,
		}
	}), nil
}

func (s *SolutionService) FindById(ctx context.Context, solutionId int64) (*types.SolutionVM, error) {
	solution, _ := s.solutionStore.FindById(ctx, solutionId)
	return s.solutionVM(solution)
}

func (s *SolutionService) solutionVM(solution *types.SolutionEntity) (*types.SolutionVM, error) {

	solverStatus := "NOT_SOLVING"
	if solution.Run.Status == types.SolvingActiveId {
		solverStatus = "SOLVING_ACTIVE"
	}

	vehicles := lo.Map(solution.SolutionDetails, func(sd *types.SolutionDetailsEntity, _ int) types.VehicleVM {
		vm := sd.Vehicle.ToVehicleVM()
		vm.Driver = types.DriverVM{
			ID:   sd.Driver.ID,
			Name: sd.Driver.Nm1,
		}
		return vm
	})

	/*grouped := lo.GroupBy(solution.SolutionDetails, func(sd *types.SolutionDetailsEntity) int64 {
		return sd.Vehicle.ID
	})

	println(grouped)*/

	vehicles = lo.UniqBy(vehicles, func(v types.VehicleVM) int64 { return v.ID })

	vehicleRouteVMS := lo.Map(vehicles, func(it types.VehicleVM, _ int) types.VehicleRouteVM {

		filteredVisits := lo.Filter(solution.SolutionDetails, func(sd *types.SolutionDetailsEntity, _ int) bool {
			return sd.Vehicle.ID == it.ID
		})

		driverEntity := filteredVisits[0].Driver

		driver := types.DriverVM{
			ID:   driverEntity.ID,
			Name: driverEntity.Nm1,
		}

		visitVMS := lo.Map(filteredVisits, func(sd *types.SolutionDetailsEntity, _ int) types.VisitVM {
			arr := strings.Split(sd.Location.LatLng, ", ")
			return types.VisitVM{
				Name:            sd.Location.UniqueID,
				Address:         sd.Location.Add1 + "、" + sd.Location.Add2 + "、" + sd.Location.Add3,
				JobID:           sd.Order.ID,
				RomsJobID:       sd.Order.RomsId,
				Type:            sd.Location.Type,
				Lat:             parseFloat(arr[0]),
				Lon:             parseFloat(arr[1]),
				Category:        sd.Order.Item.Name,
				ArrivalTime:     sd.ArrivalTime,
				ServiceDuration: sd.ServiceTime,
				DepartureTime:   sd.DepartureTime,
				DistanceToDepot: sd.TimeToDepot,
			}
		})

		jobVisits := lo.Filter(filteredVisits, func(sd *types.SolutionDetailsEntity, _ int) bool {
			return sd.Location.Type != "m_group"
		})

		orderIds := lo.GroupBy(jobVisits, func(sd *types.SolutionDetailsEntity) int64 {
			return sd.Order.ID
		})

		jobDisplayVMS := lo.MapValues(orderIds, func(sds []*types.SolutionDetailsEntity, _ int64) types.JobDisplayVM {
			pickup := lo.FindOrElse(sds, &types.SolutionDetailsEntity{}, func(sd *types.SolutionDetailsEntity) bool {
				return sd.Location.Type == "m_pickup"
			})
			delivery := lo.FindOrElse(sds, &types.SolutionDetailsEntity{}, func(sd *types.SolutionDetailsEntity) bool {
				return sd.Location.Type == "m_dest_area"
			})

			arr := strings.Split(pickup.Location.LatLng, ", ")
			pickupVal := types.VisitVM{
				Name:            pickup.Location.UniqueID,
				Address:         pickup.Location.Add1 + "、" + pickup.Location.Add2 + "、" + pickup.Location.Add3,
				JobID:           pickup.Order.ID,
				RomsJobID:       pickup.Order.RomsId,
				Type:            pickup.Location.Type,
				Lat:             parseFloat(arr[0]),
				Lon:             parseFloat(arr[1]),
				Category:        pickup.Order.Item.Name,
				ArrivalTime:     pickup.ArrivalTime,
				ServiceDuration: pickup.ServiceTime,
				DepartureTime:   pickup.DepartureTime,
				DistanceToDepot: pickup.TimeToDepot,
			}

			arr = strings.Split(delivery.Location.LatLng, ", ")
			deliveryVal := types.VisitVM{
				Name:            delivery.Location.UniqueID,
				Address:         delivery.Location.Add1 + "、" + delivery.Location.Add2 + "、" + delivery.Location.Add3,
				JobID:           delivery.Order.ID,
				RomsJobID:       delivery.Order.RomsId,
				Type:            delivery.Location.Type,
				Lat:             parseFloat(arr[0]),
				Lon:             parseFloat(arr[1]),
				Category:        delivery.Order.Item.Name,
				ArrivalTime:     delivery.ArrivalTime,
				ServiceDuration: delivery.ServiceTime,
				DepartureTime:   delivery.DepartureTime,
				DistanceToDepot: delivery.TimeToDepot,
			}

			return types.JobDisplayVM{
				JobID:     pickupVal.JobID,
				RomsJobID: pickupVal.RomsJobID,
				Material:  pickup.Order.Item.Name,
				JobIndex:  pickup.VisitIndex,
				Pickup:    pickupVal,
				Delivery:  deliveryVal,
				SetTime:   pickup.Order.SetTime,
			}
		})

		jobs := lo.Values(jobDisplayVMS)
		slices.SortFunc(jobs, func(a, b types.JobDisplayVM) int {
			return cmp.Compare(a.JobIndex, b.JobIndex)
		})

		jobs = lo.Map(jobs, func(j types.JobDisplayVM, i int) types.JobDisplayVM {
			j.JobIndex = i
			return j
		})

		return types.VehicleRouteVM{
			Vehicle: it,
			Driver:  driver,
			Visits:  visitVMS,
			Jobs:    jobs,
			Track: lo.Map(visitVMS, func(v types.VisitVM, _ int) types.Coordinates {
				return types.Coordinates{Lat: v.Lat, Lon: v.Lon}
			}),
		}
	})

	data, err := json.Marshal(vehicleRouteVMS)
	if err != nil {
		return nil, err
	}

	slices.SortFunc(vehicles, func(a, b types.VehicleVM) int {
		return strings.Compare(a.RomsID, b.RomsID)
	})

	returnValue := &types.SolutionVM{
		Date:            solution.Run.FulfillDate.Format(types.ApplicationDbDateFormat),
		RunId:           solution.RunID,
		SolutionId:      solution.ID,
		ManuallyChanged: solution.IsManual,
		TotalTime:       solution.TotalTime,
		SolverStatus:    solverStatus,
		Data:            string(data),
		VehicleRoutes:   vehicleRouteVMS,
		Vehicles:        vehicles,
	}
	return returnValue, nil
}

func parseFloat(s string) float64 {
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0.0
	}
	return f
}

func (s *SolutionService) UpdateSolution(ctx context.Context, form types.UpdateSolutionForm) (*types.SolutionChangeValidationResponse, error) {
	solution, _ := solutionStore.FindById(ctx, form.SolutionID)

	if form.GroupChanged {
		solution.SolutionDetails = lo.Map(solution.SolutionDetails, func(sd *types.SolutionDetailsEntity, _ int) *types.SolutionDetailsEntity {
			if sd.Order.ID == form.JobID {
				sd.VehicleID = form.ToVehicleID
				sd.DriverID = form.ToDriverID
			}
			return sd
		})
	}
	toProblem, err := s.getProblem(ctx, solution, form.ToVehicleID, form.ToDriverID, solution.Run.WithHistory, solution.Run.WithEvenAllocation)
	if err != nil {
		return nil, err
	}

	request := types.SolutionChangeValidationRequest{
		RunID:        solution.RunID,
		SolutionID:   solution.ID,
		ToProblem:    &toProblem,
		Changes:      form.Changes,
		GroupChanged: form.GroupChanged,
	}

	if form.GroupChanged {
		fromProblemActual, err := s.getProblem(ctx, solution, form.FromVehicleID, form.FromDriverID, solution.Run.WithHistory, solution.Run.WithEvenAllocation)
		if err != nil && err.Error() != "no solution details found" {
			return nil, err
		}
		fromProblem := &fromProblemActual
		if err != nil && err.Error() == "no solution details found" {
			fromProblem = nil
		}

		request.FromProblem = fromProblem
	}

	response, err := s.solver.ValidateSolutionChange(request)
	if err != nil {
		return nil, err
	}
	log.Default().Info("Validation message", "message", response.Messages, "err", err)

	return response, nil
}

func (s *SolutionService) getProblem(ctx context.Context, solution *types.SolutionEntity, vehicleID int64, driverID int64, withHistory, withEvenAllocation bool) (types.Problem, error) {
	details := lo.Filter(solution.SolutionDetails, func(sd *types.SolutionDetailsEntity, _ int) bool {
		return sd.VehicleID == vehicleID && sd.DriverID == driverID
	})

	if len(details) <= 0 {
		return types.Problem{}, errors.New("no solution details found")
	}

	jobIDs := lo.Map(details, func(sd *types.SolutionDetailsEntity, _ int) int64 {
		return sd.Order.ID
	})

	jobs, _ := jobStore.FindAllByIdsForValidation(ctx, jobIDs)
	problem, _ := s.makeProblemData(ctx, jobs, withHistory, withEvenAllocation)

	problem.Vehicles = lo.Filter(problem.Vehicles, func(v types.ProblemVehicle, _ int) bool {
		return v.ID == vehicleID
	})

	problem.Drivers = lo.Filter(problem.Drivers, func(d types.ProblemDriver, _ int) bool {
		return d.ID == driverID
	})
	return problem, nil
}

func (s *SolutionService) Delete(ctx context.Context, id int64) error {
	return s.solutionStore.Delete(ctx, id)
}

func (s *SolutionService) SolveAndListen(ctx context.Context, date time.Time, withHistory, withEvenAllocation bool) error {
	jobs, _ := s.getJobsByFulfillDate(ctx, date)
	problem, _ := s.makeProblemData(ctx, jobs, withHistory, withEvenAllocation)
	return s.solver.Solve(problem)
}

func (s *SolutionService) makeProblemData(ctx context.Context, jobs []types.JobDto, withHistory, withEvenAllocation bool) (types.Problem, error) {
	if len(jobs) == 0 {
		return types.Problem{}, types.ErrNoJobsFound
	}

	depotLocations, _ := s.LocationStore.FindAllDepots(ctx)

	pickupLocationIds := lo.Map(jobs, func(job types.JobDto, _ int) types.ProblemLocation {
		return toProblemPickup(*job.PickupLocation, 0)
	})

	deliveryLocationIds := lo.Map(jobs, func(job types.JobDto, _ int) types.ProblemLocation {
		return toProblemDestination(*job.DestinationLocation, 0)
	})
	locations := append(append(pickupLocationIds, deliveryLocationIds...), depotLocations...)
	locations = lo.Uniq(locations)
	locationIds := lo.Map(locations, func(item types.ProblemLocation, _ int) int64 {
		return item.ID
	})

	items := lo.Map(jobs, toProblemCategory)
	vehicles, _ := s.getVehicles(ctx, items)
	drivers, _ := s.getDrivers(ctx, items)
	distances, _ := s.getDistances(ctx, locationIds)

	problemJobs := lo.Map(jobs, toProblemJob)
	problem := types.Problem{
		Locations:          locations,
		Vehicles:           vehicles,
		Distances:          distances,
		Jobs:               problemJobs,
		Items:              items,
		Drivers:            drivers,
		WithHistory:        withHistory,
		WithEvenAllocation: withEvenAllocation,
	}
	if withHistory {
		patterns, _ := s.getPatterns(ctx)
		problem.Patterns = patterns
	} else {
		problem.Patterns = make([]types.ProblemPattern, 0)
	}
	return problem, nil
}

func (s *SolutionService) getDistances(ctx context.Context, locationIds []int64) ([]types.ProblemDistance, error) {
	distances, err := s.DistanceStore.FindAllByLocationIds(ctx, locationIds)
	if err != nil {
		return nil, fmt.Errorf("could not fetch distances: %w", err)
	}

	problemDistances := lo.Map(distances, func(d types.DistanceEntity, _ int) types.ProblemDistance {
		return types.ProblemDistance{FromLoc: d.FromLocId, ToLoc: d.ToLocId, Distance: d.Duration}
	})

	return problemDistances, nil
}

func (s *SolutionService) getPatterns(ctx context.Context) ([]types.ProblemPattern, error) {
	patterns, err := s.PatternStore.FindAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("could not fetch patterns: %w", err)
	}

	problemPatterns := lo.Map(patterns, func(p types.PatternEntity, _ int) types.ProblemPattern {
		return types.ProblemPattern{
			ShipperID:          p.ShipperID,
			PickupLocationID:   p.PickupID,
			DeliveryLocationID: p.DeliveryID,
			ItemID:             p.ItemID,
			VehicleID:          p.VehicleID,
			DriverID:           p.DriverID,
		}
	})

	return problemPatterns, nil
}

func (s *SolutionService) getVehicles(ctx context.Context, items []types.ProblemItem) ([]types.ProblemVehicle, error) {
	vehicles, err := s.VehicleStore.FindAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("could not fetch vehicles: %w", err)
	}

	problemVehicles := lo.Map(vehicles,
		func(v types.VehicleEntity, _ int) types.ProblemVehicle {
			canHandle := lo.FilterMap(v.MaterialCategories,
				func(mc types.ItemEntity, _ int) (int64, bool) {
					category, ok := lo.Find(items, func(c types.ProblemItem) bool {
						return c.ID == mc.ID
					})
					return category.ID, ok
				},
			)
			problemVehicle := v.ToProblemVehicle()
			problemVehicle.CanHandle = canHandle
			return problemVehicle
		},
	)
	return problemVehicles, nil
}

func (s *SolutionService) getDrivers(ctx context.Context, items []types.ProblemItem) ([]types.ProblemDriver, error) {
	drivers, err := s.DriverStore.FindAllByGroupId(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("could not fetch drivers: %w", err)
	}

	problemDrivers := lo.Map(drivers, func(v types.DriverEntity, _ int) types.ProblemDriver {
		canHandle := lo.FilterMap(v.MaterialCategories,
			func(mc types.ItemEntity, _ int) (int64, bool) {
				category, ok := lo.Find(items, func(c types.ProblemItem) bool {
					return c.ID == mc.ID
				})
				return category.ID, ok
			},
		)
		problemDriver := v.ToProblemDriver()
		problemDriver.CanHandle = canHandle
		problemDriver.CanDrive = lo.Map(v.Vehicles, func(v types.VehicleEntity, _ int) int64 {
			return v.ID
		})
		return problemDriver
	})
	usefulVehicles := lo.Filter(problemDrivers, func(d types.ProblemDriver, _ int) bool {
		hasPrimary := d.PrimaryVehicle != 0
		canHandleIsNotEmpty := len(d.CanHandle) > 0
		return hasPrimary || canHandleIsNotEmpty
	})
	return usefulVehicles, nil
}

func toProblemCategory(job types.JobDto, _ int) types.ProblemItem {
	return *job.Item
}

func toProblemPickup(location types.LocationDto, _ int) types.ProblemLocation {
	return location.ToProblemPickup()
}

func toProblemDestination(location types.LocationDto, _ int) types.ProblemLocation {
	return location.ToProblemDestination()
}

func toProblemJob(job types.JobDto, _ int) types.ProblemJob {
	return job.ToProblemJob()
}

func (s *SolutionService) getJobsByFulfillDate(ctx context.Context, date time.Time) ([]types.JobDto, error) {
	jobs, err := s.JobStore.FindByFulfillDate(ctx, date)
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

func (s *SolutionService) GetAllocationRunsByDate(ctx context.Context, selectedDate time.Time) ([]types.AllocationRunEntity, error) {
	return s.AllocationRunStore.FindByDate(ctx, selectedDate)
}

func (s *SolutionService) IsThereAnySolverInProgress(ctx context.Context) (bool, error) {
	count, err := s.AllocationRunStore.CountByInProgress(ctx)
	if err != nil {
		return false, fmt.Errorf("could not count in-progress solver runs: %w", err)
	}
	return count > 0, nil
}
